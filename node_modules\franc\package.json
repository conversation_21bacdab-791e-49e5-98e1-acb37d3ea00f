{"name": "franc", "threshold": 1000000, "version": "6.2.0", "description": "Detect the language of text", "license": "MIT", "keywords": ["natural", "language", "writing", "system", "detect", "guess"], "repository": "https://github.com/wooorm/franc/tree/main/packages/franc", "bugs": "https://github.com/wooorm/franc/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (http://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (http://wooorm.com)", "<PERSON><PERSON><PERSON> <k_<PERSON><PERSON><PERSON>@fastmail.fm>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["data.d.ts", "data.js", "expressions.d.ts", "expressions.js", "index.d.ts", "index.js"], "dependencies": {"trigram-utils": "^2.0.0"}, "scripts": {}, "xo": {"prettier": true, "rules": {"camelcase": "off", "max-depth": "off", "no-misleading-character-class": "off"}}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}