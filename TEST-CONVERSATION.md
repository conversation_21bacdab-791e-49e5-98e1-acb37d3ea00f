# 对话测试工具使用说明

这是一个独立的对话和LLM回复测试工具，专门用于测试多语言检测、LLM调用、文档匹配等核心功能，不涉及WhatsApp对接。

## 🎯 功能特点

- ✅ **多语言检测**：自动检测中文、英语、西班牙语、韩语、泰语、日语
- ✅ **LLM调用**：支持Gemini和OpenAI两种提供商
- ✅ **文档匹配**：智能检测回复内容并匹配相关文档
- ✅ **完整流程**：模拟真实的三阶段回复机制
- ✅ **交互测试**：支持交互式和批量测试模式

## 🚀 快速开始

### 1. 配置环境变量

确保已配置LLM API密钥：

```bash
# 使用Gemini
LLM_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key

# 或使用OpenAI
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
```

### 2. 运行测试

```bash
# 交互式测试（推荐）
npm run test-conversation

# 批量测试
npm run test-batch

# 或直接运行
node test-conversation.js interactive
node test-conversation.js batch
```

## 📱 测试模式

### 交互式测试模式

```bash
npm run test-conversation
```

- 可以输入任意消息进行测试
- 实时查看处理结果
- 输入 `quit` 退出

**示例对话**：
```
💬 请输入测试消息: 你好，升降机什么时候维修？

🔄 处理用户消息: "你好，升降机什么时候维修？"
============================================================
🌍 检测语言: zh
📱 确认消息: "Winnie 正在为您查询资料，请稍候..."
🤖 调用OPENAI...
✅ AI回复: "您好！关于升降机的维修，具体的停服务时间..."
📄 相关文档 (1个):
   1. 各座升降機例檢暫停服務事宜
      📎 https://i.mij.rip/2025/07/21/5e57701c6881d1b07e7f8ed5e46e91f9.png
      🔖 NOL-N456-2025
```

### 批量测试模式

```bash
npm run test-batch
```

自动运行预设的8个测试用例：

1. **中文-升降机维修询问**：`"你好，请问升降机什么时候维修？"`
2. **英文-电梯维修询问**：`"Hello, when is the elevator maintenance?"`
3. **西班牙文-电梯维修询问**：`"Hola, ¿cuándo es el mantenimiento del ascensor?"`
4. **韩文-电梯维修询问**：`"안녕하세요, 엘리베이터 유지보수는 언제인가요?"`
5. **中文-台风防护询问**：`"台风来了怎么办？"`
6. **英文-台风防护询问**：`"What should I do during a typhoon?"`
7. **中文-水测试询问**：`"窗户试水是什么时候？"`
8. **中文-装修投诉**：`"装修噪音太大了"`

## 🔍 测试结果解读

每个测试会显示以下信息：

### 1. 语言检测
```
🌍 检测语言: zh
```
显示系统检测到的用户语言代码。

### 2. 确认消息
```
📱 确认消息: "Winnie 正在为您查询资料，请稍候..."
```
显示系统会立即发送的多语言确认消息。

### 3. LLM回复
```
🤖 调用OPENAI...
✅ AI回复: "您好！关于升降机的维修..."
```
显示LLM提供商和AI生成的回复内容。

### 4. 相关文档
```
📄 相关文档 (1个):
   1. 各座升降機例檢暫停服務事宜
      📎 https://i.mij.rip/2025/07/21/5e57701c6881d1b07e7f8ed5e46e91f9.png
      🔖 NOL-N456-2025
```
显示系统检测到的相关文档及其图片链接。

## 🧪 测试用例建议

### 多语言测试
- 中文：`"你好，请问升降机什么时候维修？"`
- 英语：`"Hello, when is the elevator maintenance?"`
- 西班牙语：`"Hola, ¿cuándo es el mantenimiento?"`
- 韩语：`"안녕하세요, 엘리베이터 유지보수는?"`
- 泰语：`"สวัสดีค่ะ ลิฟต์ซ่อมบำรุงเมื่อไหร่คะ?"`
- 日语：`"こんにちは、エレベーターのメンテナンス？"`

### 文档匹配测试
- **升降机维护**：`"电梯维修"`, `"lift maintenance"`, `"升降機"`
- **台风防护**：`"台风来了"`, `"typhoon"`, `"防风措施"`
- **水测试**：`"窗户试水"`, `"water testing"`, `"测试"`
- **装修工程**：`"装修噪音"`, `"decoration"`, `"renovation"`

### 对话流程测试
1. 发送初始问题
2. 观察语言检测是否正确
3. 检查AI回复是否使用对应语言
4. 验证相关文档是否正确匹配

## 🔧 故障排除

### LLM API调用失败
```
❌ 处理失败: OpenAI API call failed: ...
```
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API额度是否充足

### 语言检测不准确
- 检查输入文本是否包含足够的语言特征
- 尝试使用更明显的语言关键词
- 查看语言检测逻辑是否需要调整

### 文档匹配失败
- 检查AI回复是否包含相关关键词
- 验证知识库中的文档是否正确配置
- 查看关键词匹配逻辑

## 📋 集成到生产环境

测试通过后，可以将以下核心函数集成到原server.js中：

1. **语言检测**：`detectLanguage()`
2. **多语言消息**：`getLocalizedMessage()`
3. **LLM调用**：`getLLMResponse()`, `getGeminiResponse()`, `getOpenAIResponse()`
4. **文档检测**：`detectRelatedDocuments()`

## 🎯 预期效果

- ✅ 准确检测6种语言
- ✅ 使用对应语言回复
- ✅ 智能匹配相关文档
- ✅ 完整的三阶段回复流程

## 📞 技术支持

如果遇到问题，请检查：
1. 环境变量配置是否正确
2. API密钥是否有效
3. 网络连接是否正常
4. 控制台错误日志

测试成功后即可放心集成到生产环境！🎉
