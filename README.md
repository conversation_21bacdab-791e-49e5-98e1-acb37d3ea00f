# WhatsApp Winnie AI 客服系统

一个支持多种LLM提供商的WhatsApp AI客服系统，专为Novoland住宅小区设计。

## 功能特点

- 🤖 支持多种LLM提供商：Gemini 和 OpenAI
- 🌍 智能多语言支持：中文、英语、西班牙语、韩语、泰语、日语
- 💬 WhatsApp集成，三阶段回复机制（确认→AI回复→文档图片）
- 📚 内置知识库管理
- 🖼️ 自动发送相关文档图片
- 🔄 多轮对话支持
- 🛡️ 消息去重和对话状态管理
- 📊 系统状态监控

## LLM 提供商支持

### Gemini (Google)
- 模型：gemini-2.0-flash-exp
- 适合：快速响应，成本较低
- 配置：需要 GEMINI_API_KEY

### OpenAI
- 默认模型：gpt-4o-mini
- 可配置模型：gpt-4, gpt-4-turbo, gpt-3.5-turbo 等
- 适合：高质量对话，更好的理解能力
- 配置：需要 OPENAI_API_KEY

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制示例配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入必要的配置：

```env
# WhatsApp API 配置
VERIFY_TOKEN=your_verify_token
WHATSAPP_ACCESS_TOKEN=your_whatsapp_token
PHONE_NUMBER_ID=your_phone_number_id

# 选择LLM提供商: 'gemini' 或 'openai'
LLM_PROVIDER=gemini

# Gemini配置 (当使用Gemini时)
GEMINI_API_KEY=your_gemini_api_key

# OpenAI配置 (当使用OpenAI时)
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
```

### 3. 启动服务

```bash
node server.js
```

## LLM 提供商切换

### 方法一：使用切换脚本（推荐）

```bash
# 切换到Gemini
npm run switch-gemini

# 切换到OpenAI
npm run switch-openai

# 查看当前配置
npm run config-status
```

### 方法二：手动编辑.env文件

#### 使用 Gemini

```env
LLM_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key
```

#### 使用 OpenAI

```env
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
```

### 测试LLM配置

```bash
# 测试当前配置的LLM
npm test

# 测试Gemini（临时切换）
npm run test-gemini

# 测试OpenAI（临时切换）
npm run test-openai
```

## API 端点

- `GET /` - 健康检查
- `GET /status` - 系统状态（包含LLM配置信息）
- `GET /webhook` - WhatsApp webhook验证
- `POST /webhook` - 接收WhatsApp消息
- `POST /admin/clear-cache` - 清理缓存

## 系统状态监控

访问 `http://localhost:6767/status` 查看系统状态，包括：

- 当前使用的LLM提供商
- API配置状态
- 活跃对话数量
- 内存使用情况
- 知识库状态

## 对话管理

- 支持多轮对话，自动管理对话状态
- 30分钟对话超时
- 最多30轮对话限制
- 自动清理过期对话和消息缓存

## 知识库

系统内置Novoland住宅小区的知识库，包括：
- 升降机维护通知
- 台风防护措施
- 水测试通知
- 装修工程通知

每个文档都配有对应的图片，当AI回复涉及相关内容时，会自动发送对应的文档图片。

## 多语言支持

系统支持以下语言的自动检测和回复：

| 语言 | 代码 | 示例 |
|------|------|------|
| 中文 | zh | 你好，请问升降机什么时候维修？ |
| 英语 | en | Hello, when is the elevator maintenance? |
| 西班牙语 | es | Hola, ¿cuándo es el mantenimiento del ascensor? |
| 韩语 | ko | 안녕하세요, 엘리베이터 유지보수는 언제인가요? |
| 泰语 | th | สวัสดีค่ะ ลิฟต์ซ่อมบำรุงเมื่อไหร่คะ? |
| 日语 | ja | こんにちは、エレベーターのメンテナンスはいつですか？ |

### 语言检测机制

系统使用**Franc专业语言检测库**，结合智能备用机制：

1. **Franc主检测**：使用统计模型分析文本语言特征
2. **智能映射**：将Franc语言代码映射到系统支持的语言
3. **备用检测**：当Franc无法确定时，使用字符+关键词分析
4. **容错机制**：确保在任何情况下都能返回有效的语言代码

**检测流程**：
```
文本输入 → Franc分析 → 语言映射 → 成功返回
                    ↓ 失败/不支持
                  备用检测 → 字符分析 → 关键词匹配 → 默认中文
```

详细说明请查看 [FRANC-LANGUAGE-DETECTION.md](./FRANC-LANGUAGE-DETECTION.md)

### 测试多语言功能

```bash
npm run test-multilang  # 测试语言检测功能
```

## AI智能文档检测

系统使用AI来智能判断是否需要发送相关文档图片，而不是依赖关键词匹配：

1. **AI分析**：AI分析回复内容与文档的相关性
2. **智能判断**：AI决定哪些文档对用户有帮助
3. **精准发送**：只在真正需要时发送文档图片
4. **防重复**：同一对话中不会重复发送相同图片

### AI判断流程
```
AI回复 → AI分析相关性 → 返回JSON判断 → 发送推荐文档
```

**示例**：
- 用户问：`"升降机什么时候维修？"`
- AI回复：`"关于升降机的维修通知..."`
- AI分析：`{"send_documents": [1]}`
- 系统发送：升降机维护通知图片

详细说明请查看 [AI-DOCUMENT-DETECTION.md](./AI-DOCUMENT-DETECTION.md)

### 支持的npm脚本

```bash
# 服务器运行
npm start             # 启动服务器
npm run demo          # 查看演示和帮助

# 配置管理
npm run config-status # 查看当前配置
npm run switch-gemini # 切换到Gemini
npm run switch-openai # 切换到OpenAI

# 功能测试
npm test              # 测试当前LLM
npm run test-gemini   # 测试Gemini
npm run test-openai   # 测试OpenAI
npm run test-multilang # 测试多语言功能

# 对话测试（独立测试，不涉及WhatsApp）
npm run test-conversation # 交互式对话测试
npm run test-batch    # 批量对话测试
```

## 独立对话测试

在集成到生产环境之前，您可以使用独立的对话测试工具来验证核心功能：

### 交互式测试
```bash
npm run test-conversation
```
- 可以输入任意消息进行测试
- 实时查看语言检测、LLM回复、文档匹配结果
- 不涉及WhatsApp对接，专注测试核心逻辑

### 批量测试
```bash
npm run test-batch
```
- 自动运行8个预设测试用例
- 覆盖多语言检测和文档匹配功能
- 适合快速验证整体功能

详细使用说明请查看 [TEST-CONVERSATION.md](./TEST-CONVERSATION.md)

## 注意事项

1. 确保 `.env` 文件不要提交到版本控制系统
2. 根据选择的LLM提供商，只需要配置对应的API密钥
3. OpenAI API调用可能产生费用，请注意使用量
4. Gemini API有免费额度，但也有使用限制

## 故障排除

### LLM API调用失败
- 检查API密钥是否正确
- 确认网络连接正常
- 查看控制台错误日志

### WhatsApp消息发送失败
- 检查WHATSAPP_ACCESS_TOKEN是否有效
- 确认PHONE_NUMBER_ID正确
- 验证webhook配置

## 开发

### 添加新的LLM提供商

1. 在配置部分添加新的API配置
2. 创建新的响应函数（参考 `getOpenAIResponse`）
3. 在 `getLLMResponse` 函数中添加新的条件分支
4. 更新环境变量示例和文档
