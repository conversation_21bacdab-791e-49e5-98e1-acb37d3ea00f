#!/usr/bin/env node

/**
 * LLM配置测试脚本
 * 用于测试Gemini和OpenAI的API连接
 */

require('dotenv').config();
const axios = require('axios');
const OpenAI = require('openai');

// 配置
const LLM_PROVIDER = process.env.LLM_PROVIDER || 'gemini';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || '';
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4o-mini';

console.log('=== LLM配置测试 ===');
console.log(`当前LLM提供商: ${LLM_PROVIDER}`);
console.log('');

async function testGemini() {
    console.log('🔍 测试Gemini API...');
    
    if (!GEMINI_API_KEY) {
        console.log('❌ GEMINI_API_KEY未设置');
        return false;
    }

    try {
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${GEMINI_API_KEY}`;
        
        const response = await axios({
            method: 'POST',
            url: GEMINI_API_URL,
            headers: { 'Content-Type': 'application/json' },
            data: {
                "contents": [{
                    "role": "user",
                    "parts": [{ "text": "Hello, this is a test message." }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "maxOutputTokens": 100,
                }
            },
            timeout: 10000
        });

        if (response.data.candidates && response.data.candidates.length > 0) {
            const reply = response.data.candidates[0].content.parts[0].text;
            console.log('✅ Gemini API连接成功');
            console.log(`📝 测试回复: ${reply.substring(0, 100)}...`);
            return true;
        } else {
            console.log('❌ Gemini API返回格式异常');
            return false;
        }
    } catch (error) {
        console.log('❌ Gemini API连接失败:', error.message);
        return false;
    }
}

async function testOpenAI() {
    console.log('🔍 测试OpenAI API...');
    
    if (!OPENAI_API_KEY) {
        console.log('❌ OPENAI_API_KEY未设置');
        return false;
    }

    try {
        const openai = new OpenAI({
            apiKey: OPENAI_API_KEY,
        });

        const completion = await openai.chat.completions.create({
            model: OPENAI_MODEL,
            messages: [
                { role: "user", content: "Hello, this is a test message." }
            ],
            max_tokens: 100,
            temperature: 0.7,
        });

        const reply = completion.choices[0].message.content;
        console.log('✅ OpenAI API连接成功');
        console.log(`📝 测试回复: ${reply.substring(0, 100)}...`);
        console.log(`🤖 使用模型: ${OPENAI_MODEL}`);
        return true;
    } catch (error) {
        console.log('❌ OpenAI API连接失败:', error.message);
        return false;
    }
}

async function main() {
    let success = false;

    if (LLM_PROVIDER === 'gemini') {
        success = await testGemini();
    } else if (LLM_PROVIDER === 'openai') {
        success = await testOpenAI();
    } else {
        console.log(`❌ 未知的LLM提供商: ${LLM_PROVIDER}`);
        console.log('支持的提供商: gemini, openai');
    }

    console.log('');
    console.log('=== 测试完成 ===');
    
    if (success) {
        console.log('🎉 LLM配置测试通过！可以启动服务器了。');
        process.exit(0);
    } else {
        console.log('💥 LLM配置测试失败！请检查配置。');
        process.exit(1);
    }
}

// 运行测试
main().catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
});
