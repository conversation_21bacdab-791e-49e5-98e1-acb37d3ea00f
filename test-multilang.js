#!/usr/bin/env node

/**
 * 多语言功能测试脚本
 */

require('dotenv').config();
const { franc } = require('franc');

// 使用Franc进行语言检测
function detectLanguage(text) {
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
        return 'zh'; // 默认中文
    }

    try {
        // 使用Franc检测语言
        const detectedLang = franc(text);

        // Franc语言代码映射到我们的系统
        const langMapping = {
            'cmn': 'zh',  // 中文（普通话）
            'zho': 'zh',  // 中文
            'eng': 'en',  // 英语
            'spa': 'es',  // 西班牙语
            'kor': 'ko',  // 韩语
            'tha': 'th',  // 泰语
            'jpn': 'ja',  // 日语
            'yue': 'zh',  // 粤语（归类为中文）
            'nan': 'zh',  // 闽南语（归类为中文）
        };

        // 如果Franc检测到支持的语言，使用检测结果
        if (langMapping[detectedLang]) {
            return langMapping[detectedLang];
        }

        // 如果Franc无法确定（返回'und'），使用备用检测
        if (detectedLang === 'und') {
            return fallbackLanguageDetection(text);
        }

        // 对于其他语言，使用备用检测
        return fallbackLanguageDetection(text);

    } catch (error) {
        return fallbackLanguageDetection(text);
    }
}

// 备用语言检测（基于字符和关键词）
function fallbackLanguageDetection(text) {
    const cleanText = text.toLowerCase().trim();

    // 中文检测（包含中文字符）
    if (/[\u4e00-\u9fff]/.test(text)) {
        return 'zh';
    }

    // 韩语检测
    if (/[\uac00-\ud7af\u1100-\u11ff\u3130-\u318f]/.test(text)) {
        return 'ko';
    }

    // 日语检测
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) {
        return 'ja';
    }

    // 泰语检测
    if (/[\u0e00-\u0e7f]/.test(text)) {
        return 'th';
    }

    // 西班牙语关键词检测
    const spanishKeywords = ['hola', 'gracias', 'por favor', 'ayuda', 'información', 'servicio', 'problema', 'necesito', 'quiero', 'puedo', 'cómo', 'qué', 'dónde', 'cuándo'];
    if (spanishKeywords.some(keyword => cleanText.includes(keyword))) {
        return 'es';
    }

    // 英语关键词检测
    const englishKeywords = ['hello', 'hi', 'help', 'thank', 'please', 'service', 'problem', 'need', 'want', 'can', 'how', 'what', 'where', 'when', 'maintenance', 'lift', 'elevator'];
    if (englishKeywords.some(keyword => cleanText.includes(keyword))) {
        return 'en';
    }

    // 默认返回中文
    return 'zh';
}

// 多语言消息（从server.js复制）
const LANGUAGE_MESSAGES = {
    welcome: {
        'zh': '您好！我是 Novoland 的 AI 客服助手 Winnie，已了解社区的所有基本资讯与最新公告。请问有什么可以为您服务？',
        'en': 'Hello! I am Winnie, the AI customer service assistant for Novoland. I have access to all community information and latest announcements. How may I assist you?',
        'es': '¡Hola! Soy Winnie, el asistente de servicio al cliente de IA para Novoland. Tengo acceso a toda la información de la comunidad y los últimos anuncios. ¿Cómo puedo ayudarte?',
        'ko': '안녕하세요! 저는 Novoland의 AI 고객 서비스 어시스턴트 Winnie입니다. 모든 커뮤니티 정보와 최신 공지사항에 접근할 수 있습니다. 어떻게 도와드릴까요?',
        'th': 'สวัสดีค่ะ! ฉันคือ Winnie ผู้ช่วยบริการลูกค้า AI ของ Novoland ฉันมีข้อมูลชุมชนและประกาศล่าสุดทั้งหมด มีอะไรให้ช่วยเหลือคะ?',
        'ja': 'こんにちは！私はNovolandのAIカスタマーサービスアシスタントのWinnieです。コミュニティのすべての情報と最新のお知らせにアクセスできます。何かお手伝いできることはありますか？'
    },
    processing: {
        'zh': 'Winnie 正在为您查询资料，请稍候...',
        'en': 'Winnie is searching for information for you, please wait...',
        'es': 'Winnie está buscando información para ti, por favor espera...',
        'ko': 'Winnie가 정보를 검색하고 있습니다. 잠시만 기다려 주세요...',
        'th': 'Winnie กำลังค้นหาข้อมูลให้คุณ กรุณารอสักครู่...',
        'ja': 'Winnieが情報を検索しています。少々お待ちください...'
    }
};

function getLocalizedMessage(messageType, language) {
    const messages = LANGUAGE_MESSAGES[messageType];
    if (!messages) return LANGUAGE_MESSAGES.welcome.zh;
    
    return messages[language] || messages.zh || messages.en || Object.values(messages)[0];
}

// 测试用例
const testCases = [
    { text: '你好，请问升降机什么时候维修？', expected: 'zh' },
    { text: 'Hello, when is the elevator maintenance?', expected: 'en' },
    { text: 'Hola, ¿cuándo es el mantenimiento del ascensor?', expected: 'es' },
    { text: '안녕하세요, 엘리베이터 유지보수는 언제인가요?', expected: 'ko' },
    { text: 'สวัสดีค่ะ ลิฟต์ซ่อมบำรุงเมื่อไหร่คะ?', expected: 'th' },
    { text: 'こんにちは、エレベーターのメンテナンスはいつですか？', expected: 'ja' },
    { text: 'Hi there, I need help with my apartment', expected: 'en' },
    { text: 'Necesito ayuda con mi apartamento', expected: 'es' },
    { text: '我想了解一下社区的最新公告', expected: 'zh' }
];

console.log('🌍 多语言检测测试');
console.log('===================');
console.log('');

let passedTests = 0;
let totalTests = testCases.length;

for (const testCase of testCases) {
    const detected = detectLanguage(testCase.text);
    const passed = detected === testCase.expected;
    
    console.log(`📝 测试文本: "${testCase.text}"`);
    console.log(`🎯 期望语言: ${testCase.expected}`);
    console.log(`🔍 检测结果: ${detected}`);
    console.log(`✅ 结果: ${passed ? '通过' : '失败'}`);
    
    if (passed) {
        passedTests++;
        
        // 显示对应的欢迎消息
        const welcomeMsg = getLocalizedMessage('welcome', detected);
        const processingMsg = getLocalizedMessage('processing', detected);
        
        console.log(`💬 欢迎消息: "${welcomeMsg.substring(0, 50)}..."`);
        console.log(`⏳ 处理消息: "${processingMsg}"`);
    }
    
    console.log('');
}

console.log('===================');
console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);

if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！多语言功能正常工作。');
    process.exit(0);
} else {
    console.log('❌ 部分测试失败，请检查语言检测逻辑。');
    process.exit(1);
}
