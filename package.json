{"name": "whatsapp-webhook", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "test": "node test-llm.js", "test-gemini": "LLM_PROVIDER=gemini node test-llm.js", "test-openai": "LLM_PROVIDER=openai node test-llm.js", "test-multilang": "node test-multilang.js", "switch-gemini": "node switch-llm.js gemini", "switch-openai": "node switch-llm.js openai", "config-status": "node switch-llm.js status", "demo": "node demo.js", "dev": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "body-parser": "^2.2.0", "dotenv": "^17.2.0", "express": "^5.1.0", "openai": "^5.10.1"}}