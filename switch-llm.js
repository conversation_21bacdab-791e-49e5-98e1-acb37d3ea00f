#!/usr/bin/env node

/**
 * LLM提供商切换脚本
 * 用于快速切换Gemini和OpenAI
 */

const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '.env');

function readEnvFile() {
    if (!fs.existsSync(envPath)) {
        console.log('❌ .env文件不存在，请先创建.env文件');
        console.log('💡 可以复制.env.example文件：cp .env.example .env');
        process.exit(1);
    }
    
    return fs.readFileSync(envPath, 'utf8');
}

function writeEnvFile(content) {
    fs.writeFileSync(envPath, content, 'utf8');
}

function switchToGemini() {
    console.log('🔄 切换到Gemini...');
    
    let envContent = readEnvFile();
    
    // 更新LLM_PROVIDER
    if (envContent.includes('LLM_PROVIDER=')) {
        envContent = envContent.replace(/LLM_PROVIDER=.*/g, 'LLM_PROVIDER=gemini');
    } else {
        envContent += '\nLLM_PROVIDER=gemini\n';
    }
    
    writeEnvFile(envContent);
    console.log('✅ 已切换到Gemini');
    console.log('📝 请确保已设置GEMINI_API_KEY');
}

function switchToOpenAI() {
    console.log('🔄 切换到OpenAI...');
    
    let envContent = readEnvFile();
    
    // 更新LLM_PROVIDER
    if (envContent.includes('LLM_PROVIDER=')) {
        envContent = envContent.replace(/LLM_PROVIDER=.*/g, 'LLM_PROVIDER=openai');
    } else {
        envContent += '\nLLM_PROVIDER=openai\n';
    }
    
    writeEnvFile(envContent);
    console.log('✅ 已切换到OpenAI');
    console.log('📝 请确保已设置OPENAI_API_KEY');
}

function showCurrentConfig() {
    const envContent = readEnvFile();
    const lines = envContent.split('\n');
    
    console.log('📋 当前配置:');
    console.log('');
    
    for (const line of lines) {
        if (line.startsWith('LLM_PROVIDER=')) {
            console.log(`🤖 ${line}`);
        } else if (line.startsWith('GEMINI_API_KEY=')) {
            const hasKey = line.split('=')[1] && line.split('=')[1].trim() !== '';
            console.log(`🔑 GEMINI_API_KEY=${hasKey ? '已设置' : '未设置'}`);
        } else if (line.startsWith('OPENAI_API_KEY=')) {
            const hasKey = line.split('=')[1] && line.split('=')[1].trim() !== '';
            console.log(`🔑 OPENAI_API_KEY=${hasKey ? '已设置' : '未设置'}`);
        } else if (line.startsWith('OPENAI_MODEL=')) {
            console.log(`🎯 ${line}`);
        }
    }
}

function showHelp() {
    console.log('🔧 LLM提供商切换工具');
    console.log('');
    console.log('用法:');
    console.log('  node switch-llm.js gemini    # 切换到Gemini');
    console.log('  node switch-llm.js openai   # 切换到OpenAI');
    console.log('  node switch-llm.js status   # 显示当前配置');
    console.log('  node switch-llm.js help     # 显示帮助');
    console.log('');
    console.log('或者使用npm脚本:');
    console.log('  npm run test-gemini          # 测试Gemini配置');
    console.log('  npm run test-openai          # 测试OpenAI配置');
}

// 主程序
const command = process.argv[2];

switch (command) {
    case 'gemini':
        switchToGemini();
        break;
    case 'openai':
        switchToOpenAI();
        break;
    case 'status':
        showCurrentConfig();
        break;
    case 'help':
    case '--help':
    case '-h':
        showHelp();
        break;
    default:
        console.log('❌ 未知命令:', command || '(无)');
        console.log('');
        showHelp();
        process.exit(1);
}
