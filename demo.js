#!/usr/bin/env node

/**
 * 演示脚本 - 展示如何使用不同的LLM提供商
 */

require('dotenv').config();

console.log('🎯 WhatsApp Winnie AI 客服系统演示');
console.log('=====================================');
console.log('');

// 显示当前配置
const LLM_PROVIDER = process.env.LLM_PROVIDER || 'gemini';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || '';
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4o-mini';

console.log('📋 当前配置:');
console.log(`🤖 LLM提供商: ${LLM_PROVIDER}`);

if (LLM_PROVIDER === 'gemini') {
    console.log(`🔑 Gemini API: ${GEMINI_API_KEY ? '已配置' : '未配置'}`);
    console.log(`🎯 模型: gemini-2.0-flash-exp`);
} else if (LLM_PROVIDER === 'openai') {
    console.log(`🔑 OpenAI API: ${OPENAI_API_KEY ? '已配置' : '未配置'}`);
    console.log(`🎯 模型: ${OPENAI_MODEL}`);
}

console.log('');
console.log('🚀 可用命令:');
console.log('');

console.log('📦 安装和启动:');
console.log('  npm install              # 安装依赖');
console.log('  npm start                # 启动服务器');
console.log('');

console.log('🔧 配置管理:');
console.log('  npm run config-status    # 查看当前配置');
console.log('  npm run switch-gemini    # 切换到Gemini');
console.log('  npm run switch-openai    # 切换到OpenAI');
console.log('');

console.log('🧪 测试:');
console.log('  npm test                 # 测试当前LLM配置');
console.log('  npm run test-gemini      # 测试Gemini');
console.log('  npm run test-openai      # 测试OpenAI');
console.log('');

console.log('📊 监控:');
console.log('  curl http://localhost:6767/status  # 查看系统状态');
console.log('  curl http://localhost:6767/        # 健康检查');
console.log('');

console.log('💡 使用建议:');
console.log('');

if (!GEMINI_API_KEY && !OPENAI_API_KEY) {
    console.log('⚠️  请先配置API密钥:');
    console.log('   1. 复制配置文件: cp .env.example .env');
    console.log('   2. 编辑.env文件，填入API密钥');
    console.log('   3. 运行测试: npm test');
} else if (LLM_PROVIDER === 'gemini' && GEMINI_API_KEY) {
    console.log('✅ Gemini已配置，可以开始使用');
    console.log('   运行: npm test 来测试连接');
} else if (LLM_PROVIDER === 'openai' && OPENAI_API_KEY) {
    console.log('✅ OpenAI已配置，可以开始使用');
    console.log('   运行: npm test 来测试连接');
} else {
    console.log('⚠️  当前LLM提供商的API密钥未配置');
    console.log(`   请在.env文件中设置 ${LLM_PROVIDER.toUpperCase()}_API_KEY`);
}

console.log('');
console.log('🔄 切换LLM提供商:');
console.log('   Gemini: 免费额度大，响应快');
console.log('   OpenAI: 质量高，理解能力强（付费）');
console.log('');

console.log('📚 更多信息请查看 README.md');
console.log('=====================================');
