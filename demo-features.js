#!/usr/bin/env node

/**
 * 功能演示脚本 - 展示新增的多语言和文档图片功能
 */

require('dotenv').config();

console.log('🎯 WhatsApp Winnie AI 新功能演示');
console.log('=====================================');
console.log('');

console.log('🆕 新增功能概览:');
console.log('');

console.log('1️⃣ 智能多语言支持');
console.log('   ✅ 自动检测用户语言（中/英/西/韩/泰/日）');
console.log('   ✅ 使用对应语言回复欢迎和处理消息');
console.log('   ✅ AI回复也会使用检测到的语言');
console.log('');

console.log('2️⃣ 自动文档图片发送');
console.log('   ✅ 智能检测回复内容关键词');
console.log('   ✅ 自动发送相关知识库文档图片');
console.log('   ✅ 防重复发送机制');
console.log('   ✅ 支持图片说明文字');
console.log('');

console.log('3️⃣ 三阶段回复机制');
console.log('   📝 第一阶段：立即确认（多语言）');
console.log('   🤖 第二阶段：AI智能回复');
console.log('   🖼️ 第三阶段：相关文档图片');
console.log('');

console.log('🌍 支持的语言:');
console.log('');

const languages = [
    { code: 'zh', name: '中文', example: '你好，请问升降机什么时候维修？' },
    { code: 'en', name: 'English', example: 'Hello, when is the elevator maintenance?' },
    { code: 'es', name: 'Español', example: 'Hola, ¿cuándo es el mantenimiento del ascensor?' },
    { code: 'ko', name: '한국어', example: '안녕하세요, 엘리베이터 유지보수는 언제인가요?' },
    { code: 'th', name: 'ไทย', example: 'สวัสดีค่ะ ลิฟต์ซ่อมบำรุงเมื่อไหร่คะ?' },
    { code: 'ja', name: '日本語', example: 'こんにちは、エレベーターのメンテナンスはいつですか？' }
];

languages.forEach((lang, index) => {
    console.log(`${index + 1}. ${lang.name} (${lang.code})`);
    console.log(`   示例: "${lang.example}"`);
    console.log('');
});

console.log('📄 知识库文档 (配有图片):');
console.log('');

const documents = [
    { name: '升降机维护通知', keywords: ['升降機', '电梯', 'lift', 'elevator', '维修', 'maintenance'] },
    { name: '台风防护措施', keywords: ['台风', '防风', 'typhoon', '风暴', 'storm'] },
    { name: '水测试通知', keywords: ['试水', '水测试', 'water testing', '窗户', 'window'] },
    { name: '装修工程通知', keywords: ['装修', '装潢', 'decoration', 'renovation', '噪音'] }
];

documents.forEach((doc, index) => {
    console.log(`${index + 1}. ${doc.name}`);
    console.log(`   触发关键词: ${doc.keywords.join(', ')}`);
    console.log('');
});

console.log('🔧 测试命令:');
console.log('');

console.log('# 测试多语言检测');
console.log('npm run test-multilang');
console.log('');

console.log('# 测试LLM配置');
console.log('npm test');
console.log('');

console.log('# 启动服务器');
console.log('npm start');
console.log('');

console.log('📱 WhatsApp测试流程:');
console.log('');

console.log('1. 发送不同语言的消息测试语言检测');
console.log('2. 询问升降机维修相关问题');
console.log('3. 观察三阶段回复:');
console.log('   - 立即确认消息（对应语言）');
console.log('   - AI智能回复（对应语言）');
console.log('   - 升降机维护通知图片');
console.log('');

console.log('🎯 预期效果:');
console.log('');

console.log('✅ 用户发送: "Hello, when is the lift maintenance?"');
console.log('📱 立即回复: "Winnie is searching for information..."');
console.log('🤖 AI回复: "Hello! The lift maintenance is scheduled..."');
console.log('🖼️ 图片: 升降机维护通知文档图片');
console.log('');

console.log('✅ 用户发送: "你好，升降机什么时候维修？"');
console.log('📱 立即回复: "Winnie 正在为您查询资料，请稍候..."');
console.log('🤖 AI回复: "您好！根据最新公告，升降机维修安排..."');
console.log('🖼️ 图片: 升降机维护通知文档图片');
console.log('');

console.log('🚀 开始使用:');
console.log('');

console.log('1. 确保已配置API密钥');
console.log('2. 运行: npm start');
console.log('3. 配置WhatsApp webhook');
console.log('4. 开始测试多语言对话！');
console.log('');

console.log('=====================================');
console.log('🎉 新功能已就绪，开始体验吧！');
