# AI智能文档检测机制

## 🎯 概述

系统现在使用AI来智能判断是否需要发送相关文档图片，而不是依赖关键词匹配。这种方法更加智能、准确，能够避免过度发送或遗漏重要文档。

## 🔄 工作流程

### 1. 三阶段回复机制
```
用户消息 → 语言检测 → 立即确认回复
                    ↓
                AI生成回复
                    ↓
                AI分析回复内容 → 判断相关文档 → 发送图片
```

### 2. AI文档分析过程

#### 步骤1：构建文档列表
系统将知识库中的所有文档整理成结构化列表：
```
1. 各座升降機例檢暫停服務事宜 / Suspension of Lift Services System for Routine Inspection (NOL-N456-2025)
2. 有關:家居防風措施事宜 / Re: Typhoon Prevention of Measures (NOL-N077-2023)
3. 有關:個別單位室外試水事宜 / Re : Outdoor water testing at individual unit (NOL-N053-2023)
4. 有關:NOVOLAND Elverum 2 單位裝修工程事宜 / Re: NOVOLAND Elverum 2 Unit Decoration Work (NOL-N034-2023)
```

#### 步骤2：AI分析提示
系统向AI发送分析提示：
```
请分析以下AI回复内容，判断是否需要发送相关的文档图片。

AI回复内容：
"您好！关于升降机的维修通知，请查阅以下信息..."

可用文档列表：
[文档列表]

请判断：
1. AI回复是否涉及到上述任何文档的内容？
2. 如果涉及，用户是否会从看到相关文档图片中受益？
3. 哪些文档与回复内容最相关？

请只返回JSON格式的结果：
{"send_documents": [1, 2]} 或 {"send_documents": []}
```

#### 步骤3：AI判断结果
AI返回JSON格式的判断结果：
- `{"send_documents": [1]}` - 发送第1个文档
- `{"send_documents": [2, 3]}` - 发送第2和第3个文档
- `{"send_documents": []}` - 不发送任何文档

#### 步骤4：执行发送
系统根据AI的判断结果发送对应的文档图片。

## 📊 测试结果

### 成功案例

| 用户输入 | AI回复关键内容 | AI判断 | 发送文档 |
|----------|----------------|--------|----------|
| "升降机什么时候维修？" | "关于升降机的维修通知..." | `[1]` | 升降机维护通知 |
| "台风来了怎么办？" | "建议您采取以下防风措施..." | `[2]` | 台风防护措施 |
| "窗户试水是什么时候？" | "有关个别单位室外试水..." | `[3]` | 水测试通知 |
| "装修噪音太大了" | "对于装修产生的噪音..." | `[4]` | 装修工程通知 |

### 多语言支持

AI判断机制支持所有语言的回复分析：

**中文示例**：
- 输入：`"你好，请问升降机什么时候维修？"`
- AI回复：`"您好！关于升降机的维修通知..."`
- AI判断：`{"send_documents": [1]}`

**英文示例**：
- 输入：`"Hello, when is the elevator maintenance?"`
- AI回复：`"The lift services will be temporarily suspended..."`
- AI判断：`{"send_documents": [1]}`

**西班牙文示例**：
- 输入：`"Hola, ¿cuándo es el mantenimiento del ascensor?"`
- AI回复：`"Hola, el mantenimiento del ascensor está programado..."`
- AI判断：`{"send_documents": [1]}`

## 🔧 技术实现

### 核心函数

#### 1. 主要检测函数
```javascript
async function detectAndSendRelatedDocuments(to, responseText, knowledgeBase) {
    // 构建文档列表
    // 调用AI分析
    // 解析结果
    // 发送文档图片
}
```

#### 2. AI分析函数
```javascript
// Gemini分析
async function getDocumentAnalysisGemini(prompt)

// OpenAI分析  
async function getDocumentAnalysisOpenAI(prompt)
```

### 配置参数

AI分析使用较低的温度值以确保一致性：
- **Temperature**: 0.1 (降低随机性)
- **Max Tokens**: 200 (限制输出长度)
- **Timeout**: 15秒

## 🎯 优势

### 相比关键词匹配的优势

1. **智能理解**：AI能理解上下文和语义，不仅仅是关键词
2. **减少误判**：避免因关键词重叠导致的错误匹配
3. **多语言支持**：无需为每种语言维护关键词列表
4. **灵活适应**：能处理各种表达方式和同义词
5. **避免过度发送**：AI会判断用户是否真正需要文档

### 实际效果

- ✅ **准确性提升**：AI能准确判断回复内容与文档的相关性
- ✅ **用户体验**：只在真正需要时发送文档，避免信息过载
- ✅ **维护简便**：无需维护复杂的关键词规则
- ✅ **扩展性强**：添加新文档时无需更新匹配规则

## 🚀 使用方法

### 在生产环境中启用

1. **确保AI配置**：
   ```bash
   # 使用OpenAI
   LLM_PROVIDER=openai
   OPENAI_API_KEY=your_key
   
   # 或使用Gemini
   LLM_PROVIDER=gemini
   GEMINI_API_KEY=your_key
   ```

2. **测试功能**：
   ```bash
   npm run test-batch  # 批量测试AI判断功能
   ```

3. **部署到生产**：
   - 新的`detectAndSendRelatedDocuments`函数已集成到server.js
   - 保持原有WhatsApp对接不变
   - AI会自动分析每个回复并智能发送文档

### 监控和调试

系统会输出详细的日志：
```
--- [DOC] AI analyzing for related documents ---
--- [DOC] AI analysis result: {"send_documents": [1]} ---
--- [DOC] AI recommended sending: 各座升降機例檢暫停服務事宜 ---
--- [DOC] AI recommended and sent 1 document(s) ---
```

## 🔍 故障排除

### 常见问题

1. **AI分析失败**：
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看错误日志

2. **文档不发送**：
   - AI可能判断不需要发送文档
   - 检查AI回复内容是否真正涉及文档
   - 查看AI分析结果日志

3. **发送错误文档**：
   - AI判断可能需要调整
   - 检查文档列表是否正确
   - 考虑优化分析提示

### 调试技巧

- 使用测试工具查看AI分析过程
- 检查控制台输出的AI判断结果
- 验证文档URL是否正确

## 📈 未来改进

1. **学习机制**：根据用户反馈优化AI判断
2. **个性化**：基于用户历史调整发送策略
3. **批量分析**：一次分析多个相关文档
4. **置信度**：AI返回判断的置信度分数

AI智能文档检测让系统更加智能和用户友好！🎉
