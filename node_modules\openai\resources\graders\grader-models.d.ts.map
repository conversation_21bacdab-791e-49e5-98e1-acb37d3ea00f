{"version": 3, "file": "grader-models.d.ts", "sourceRoot": "", "sources": ["../../src/resources/graders/grader-models.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,YAAY;AAExB,qBAAa,YAAa,SAAQ,WAAW;CAAG;AAEhD;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAErC;;OAEG;IACH,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEtB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE9B;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;CACrB;AAED,yBAAiB,gBAAgB,CAAC;IAChC;;;;;;OAMG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,OAAO,EAAE,MAAM,GAAG,YAAY,CAAC,iBAAiB,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;QAExG;;;WAGG;QACH,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,CAAC;QAEpD;;WAEG;QACH,IAAI,CAAC,EAAE,SAAS,CAAC;KAClB;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,UAAU;YACzB;;eAEG;YACH,IAAI,EAAE,MAAM,CAAC;YAEb;;eAEG;YACH,IAAI,EAAE,aAAa,CAAC;SACrB;QAED;;WAEG;QACH,UAAiB,UAAU;YACzB;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;YAElB;;eAEG;YACH,IAAI,EAAE,aAAa,CAAC;YAEpB;;;eAGG;YACH,MAAM,CAAC,EAAE,MAAM,CAAC;SACjB;KACF;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;IAEzB;;;OAGG;IACH,OAAO,EAAE,iBAAiB,GAAG,oBAAoB,GAAG,YAAY,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;IAEvG;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,IAAI,EAAE,QAAQ,CAAC;IAEf;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IAErC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;IAEpB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAEtB;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;AAED,yBAAiB,gBAAgB,CAAC;IAChC;;;;;;OAMG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,OAAO,EAAE,MAAM,GAAG,YAAY,CAAC,iBAAiB,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;QAExG;;;WAGG;QACH,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,CAAC;QAEpD;;WAEG;QACH,IAAI,CAAC,EAAE,SAAS,CAAC;KAClB;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,UAAU;YACzB;;eAEG;YACH,IAAI,EAAE,MAAM,CAAC;YAEb;;eAEG;YACH,IAAI,EAAE,aAAa,CAAC;SACrB;QAED;;WAEG;QACH,UAAiB,UAAU;YACzB;;eAEG;YACH,SAAS,EAAE,MAAM,CAAC;YAElB;;eAEG;YACH,IAAI,EAAE,aAAa,CAAC;YAEpB;;;eAGG;YACH,MAAM,CAAC,EAAE,MAAM,CAAC;SACjB;KACF;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC;IAE1C;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACnC;;;OAGG;IACH,iBAAiB,EACb,aAAa,GACb,MAAM,GACN,MAAM,GACN,QAAQ,GACR,SAAS,GACT,SAAS,GACT,SAAS,GACT,SAAS,GACT,SAAS,GACT,SAAS,CAAC;IAEd;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;CACzB;AAED,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC,OAAO,EACL,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,YAAY,IAAI,YAAY,EACjC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,oBAAoB,IAAI,oBAAoB,GAClD,CAAC;CACH"}