#!/usr/bin/env node

/**
 * 对话和LLM回复测试脚本
 * 独立测试多语言检测、LLM调用、文档匹配等核心功能
 * 不涉及WhatsApp对接
 */

require('dotenv').config();
const axios = require('axios');
const OpenAI = require('openai');

// ===== 配置部分 (从server.js复制) =====
const LLM_PROVIDER = process.env.LLM_PROVIDER || 'gemini';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || '';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${GEMINI_API_KEY}`;
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4o-mini';

// 初始化 OpenAI 客户端
let openai = null;
if (OPENAI_API_KEY) {
    openai = new OpenAI({
        apiKey: OPENAI_API_KEY,
    });
}

// ===== 多语言支持 (从server.js复制) =====
const LANGUAGE_MESSAGES = {
    welcome: {
        'zh': '您好！我是 Novoland 的 AI 客服助手 Winnie，已了解社区的所有基本资讯与最新公告。请问有什么可以为您服务？',
        'en': 'Hello! I am Winnie, the AI customer service assistant for Novoland. I have access to all community information and latest announcements. How may I assist you?',
        'es': '¡Hola! Soy Winnie, el asistente de servicio al cliente de IA para Novoland. Tengo acceso a toda la información de la comunidad y los últimos anuncios. ¿Cómo puedo ayudarte?',
        'ko': '안녕하세요! 저는 Novoland의 AI 고객 서비스 어시스턴트 Winnie입니다. 모든 커뮤니티 정보와 최신 공지사항에 접근할 수 있습니다. 어떻게 도와드릴까요?',
        'th': 'สวัสดีค่ะ! ฉันคือ Winnie ผู้ช่วยบริการลูกค้า AI ของ Novoland ฉันมีข้อมูลชุมชนและประกาศล่าสุดทั้งหมด มีอะไรให้ช่วยเหลือคะ?',
        'ja': 'こんにちは！私はNovolandのAIカスタマーサービスアシスタントのWinnieです。コミュニティのすべての情報と最新のお知らせにアクセスできます。何かお手伝いできることはありますか？'
    },
    processing: {
        'zh': 'Winnie 正在为您查询资料，请稍候...',
        'en': 'Winnie is searching for information for you, please wait...',
        'es': 'Winnie está buscando información para ti, por favor espera...',
        'ko': 'Winnie가 정보를 검색하고 있습니다. 잠시만 기다려 주세요...',
        'th': 'Winnie กำลังค้นหาข้อมูลให้คุณ กรุณารอสักครู่...',
        'ja': 'Winnieが情報を検索しています。少々お待ちください...'
    }
};

// ===== 知识库 (简化版，包含doc_url) =====
const NOVOLOG_KNOWLEDGE_BASE = {
    "notice_lift_maintenance_NOL-N456-2025.pdf": {
        "metadata": { "file_name": "notice_lift_maintenance_NOL-N456-2025.pdf", "reference_no": "NOL-N456-2025" },
        "title": { "zh_hk": "各座升降機例檢暫停服務事宜", "en_us": "Suspension of Lift Services System for Routine Inspection" },
        "doc_url": "https://i.mij.rip/2025/07/21/5e57701c6881d1b07e7f8ed5e46e91f9.png"
    },
    "notice_typhoon_prevention_NOL-N077-2023.pdf": {
        "metadata": { "file_name": "notice_typhoon_prevention_NOL-N077-2023.pdf", "reference_no": "NOL-N077-2023" },
        "title": { "zh_hk": "有關:家居防風措施事宜", "en_us": "Re: Typhoon Prevention of Measures" },
        "doc_url": "https://i.mij.rip/2025/07/21/1fc9d9341f202ce0aa0758682e2deaec.png"
    },
    "notice_water_testing_NOL-N053-2023.pdf": {
        "metadata": { "file_name": "notice_water_testing_NOL-N053-2023.pdf", "reference_no": "NOL-N053-2023" },
        "title": { "zh_hk": "有關:個別單位室外試水事宜", "en_us": "Re : Outdoor water testing at individual unit" },
        "doc_url": "https://i.mij.rip/2025/07/21/593de6821f714653b50bd475847eda8f.png"
    },
    "notice_decoration_work_NOL-N034-2023.pdf": {
        "metadata": { "file_name": "notice_decoration_work_NOL-N034-2023.pdf", "reference_no": "NOL-N034-2023" },
        "title": { "zh_hk": "有關:NOVOLAND Elverum 2 單位裝修工程事宜", "en_us": "Re: NOVOLAND Elverum 2 Unit Decoration Work" },
        "doc_url": "https://i.mij.rip/2025/07/21/debfba67b07bd1d62d88a4fb22eddbf6.png"
    }
};

// ===== 核心函数 (从server.js复制) =====

// 语言检测函数
function detectLanguage(text) {
    if (!text || typeof text !== 'string') return 'zh';
    
    const cleanText = text.toLowerCase().trim();
    
    // 中文检测（包含中文字符）
    if (/[\u4e00-\u9fff]/.test(text)) {
        return 'zh';
    }
    
    // 韩语检测
    if (/[\uac00-\ud7af\u1100-\u11ff\u3130-\u318f]/.test(text)) {
        return 'ko';
    }
    
    // 日语检测
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) {
        return 'ja';
    }
    
    // 泰语检测
    if (/[\u0e00-\u0e7f]/.test(text)) {
        return 'th';
    }
    
    // 西班牙语关键词检测
    const spanishKeywords = ['hola', 'gracias', 'por favor', 'ayuda', 'información', 'servicio', 'problema', 'necesito', 'quiero', 'puedo', 'cómo', 'qué', 'dónde', 'cuándo'];
    if (spanishKeywords.some(keyword => cleanText.includes(keyword))) {
        return 'es';
    }
    
    // 英语关键词检测
    const englishKeywords = ['hello', 'hi', 'help', 'thank', 'please', 'service', 'problem', 'need', 'want', 'can', 'how', 'what', 'where', 'when', 'maintenance', 'lift', 'elevator'];
    if (englishKeywords.some(keyword => cleanText.includes(keyword))) {
        return 'en';
    }
    
    // 默认返回中文
    return 'zh';
}

// 获取多语言消息
function getLocalizedMessage(messageType, language) {
    const messages = LANGUAGE_MESSAGES[messageType];
    if (!messages) return LANGUAGE_MESSAGES.welcome.zh;
    
    return messages[language] || messages.zh || messages.en || Object.values(messages)[0];
}

// 对话状态结构
function createConversationState(userId) {
    return {
        userId: userId,
        rounds: 0,
        startTime: Date.now(),
        lastActivity: Date.now(),
        conversationHistory: [],
        detectedLanguage: 'zh'
    };
}

// 检测相关文档
function detectRelatedDocuments(responseText, knowledgeBase) {
    if (!responseText || !knowledgeBase) return [];

    const lowerResponseText = responseText.toLowerCase();
    const relatedDocs = [];
    
    // 检查每个知识库文档
    for (const [docKey, docData] of Object.entries(knowledgeBase)) {
        if (!docData.doc_url) continue;
        
        let shouldInclude = false;
        
        // 检查标题关键词
        if (docData.title) {
            const titleKeywords = [
                ...(docData.title.zh_hk ? docData.title.zh_hk.split(/[，。！？\s]+/) : []),
                ...(docData.title.en_us ? docData.title.en_us.split(/[,.\s]+/) : [])
            ].filter(keyword => keyword.length > 2);
            
            if (titleKeywords.some(keyword => lowerResponseText.includes(keyword.toLowerCase()))) {
                shouldInclude = true;
            }
        }
        
        // 检查特定关键词
        const documentKeywords = {
            'lift': ['升降機', '电梯', 'lift', 'elevator', '维修', 'maintenance', '检验', 'inspection'],
            'typhoon': ['台风', '防风', 'typhoon', '风暴', 'storm', '防护', 'prevention'],
            'water': ['试水', '水测试', 'water testing', '窗户', 'window', '测试', 'testing'],
            'decoration': ['装修', '装潢', 'decoration', 'renovation', '工程', 'work', '噪音', 'noise']
        };
        
        // 根据文档类型检查关键词
        for (const [docType, keywords] of Object.entries(documentKeywords)) {
            if (docKey.includes(docType)) {
                if (keywords.some(keyword => lowerResponseText.includes(keyword.toLowerCase()))) {
                    shouldInclude = true;
                    break;
                }
            }
        }
        
        if (shouldInclude) {
            relatedDocs.push({
                key: docKey,
                title: docData.title?.zh_hk || docData.title?.en_us || docKey,
                doc_url: docData.doc_url,
                reference_no: docData.metadata?.reference_no
            });
        }
    }
    
    return relatedDocs;
}

// ===== LLM调用函数 =====

// 通用LLM调用函数
async function getLLMResponse(userInput, knowledgeBase, conversationState) {
    if (LLM_PROVIDER === 'openai') {
        return await getOpenAIResponse(userInput, knowledgeBase, conversationState);
    } else {
        return await getGeminiResponse(userInput, knowledgeBase, conversationState);
    }
}

// Gemini API调用
async function getGeminiResponse(userInput, knowledgeBase, conversationState) {
    const knowledgeBaseString = JSON.stringify(knowledgeBase, null, 2);
    const systemPrompt = `
#1. 总体目标
你将扮演「Winnie」，一个专为「Novoland」高档住宅小区设计的 AI 客服助手。

#2. 角色设定：Winnie
- **身份**：Novoland 住宅小区的 AI 客服总管。
- **语气与风格**：温暖专业、耐心有礼、用词清晰。
- **语言**：系统已检测到用户语言为：${conversationState.detectedLanguage}，请使用此语言回复。

#3. 核心知识库
${knowledgeBaseString}

#4. 输出格式
- 你的回复应该是简洁的纯文本格式，适合消息显示。
- 直接提供友善、专业的文本回复。
`;

    let contents = [];

    contents.push({
        role: "user",
        parts: [{ text: systemPrompt }]
    });

    contents.push({
        role: "model",
        parts: [{ text: "您好！我是 Novoland 的 AI 客服助手 Winnie，已了解社区的所有基本资讯与最新公告。请问有什么可以为您服务？" }]
    });

    // 添加历史对话记录
    if (conversationState.conversationHistory && conversationState.conversationHistory.length > 2) {
        const historyWithoutSystem = conversationState.conversationHistory.slice(2);
        contents.push(...historyWithoutSystem);
    }

    contents.push({
        role: "user",
        parts: [{ text: userInput }]
    });

    try {
        const response = await axios({
            method: 'POST',
            url: GEMINI_API_URL,
            headers: { 'Content-Type': 'application/json' },
            data: {
                "contents": contents,
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            },
            timeout: 30000
        });

        const responseData = response.data;

        if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error('Gemini API returned no candidates in response.');
        }

        const geminiResponse = responseData.candidates[0].content.parts[0].text;

        contents.push({
            role: "model",
            parts: [{ text: geminiResponse }]
        });

        // 保持最近10轮对话
        if (contents.length > 20) {
            contents = contents.slice(-20);
        }
        conversationState.conversationHistory = contents;

        return geminiResponse;
    } catch (error) {
        console.error('Gemini API call failed:', error.message);
        return `抱歉，AI 服务暂时无法连线，建议您稍后再试或联系物业服务专线 2656 3800。错误讯息: ${error.message}`;
    }
}

// OpenAI API调用
async function getOpenAIResponse(userInput, knowledgeBase, conversationState) {
    if (!openai) {
        throw new Error('OpenAI client not initialized. Please check OPENAI_API_KEY.');
    }

    const knowledgeBaseString = JSON.stringify(knowledgeBase, null, 2);
    const systemPrompt = `
#1. 总体目标
你将扮演「Winnie」，一个专为「Novoland」高档住宅小区设计的 AI 客服助手。

#2. 角色设定：Winnie
- **身份**：Novoland 住宅小区的 AI 客服总管。
- **语气与风格**：温暖专业、耐心有礼、用词清晰。
- **语言**：系统已检测到用户语言为：${conversationState.detectedLanguage}，请使用此语言回复。

#3. 核心知识库
${knowledgeBaseString}

#4. 输出格式
- 你的回复应该是简洁的纯文本格式，适合消息显示。
- 直接提供友善、专业的文本回复。
`;

    try {
        let messages = [
            {
                role: "system",
                content: systemPrompt
            }
        ];

        // 添加对话历史
        if (conversationState.conversationHistory && conversationState.conversationHistory.length > 0) {
            for (const item of conversationState.conversationHistory) {
                if (item.role === "user") {
                    messages.push({
                        role: "user",
                        content: item.parts[0].text
                    });
                } else if (item.role === "model") {
                    messages.push({
                        role: "assistant",
                        content: item.parts[0].text
                    });
                }
            }
        }

        messages.push({
            role: "user",
            content: userInput
        });

        const completion = await openai.chat.completions.create({
            model: OPENAI_MODEL,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1024,
        });

        const openaiResponse = completion.choices[0].message.content;

        // 更新对话历史
        if (!conversationState.conversationHistory) {
            conversationState.conversationHistory = [];
        }

        conversationState.conversationHistory.push({
            role: "user",
            parts: [{ text: userInput }]
        });

        conversationState.conversationHistory.push({
            role: "model",
            parts: [{ text: openaiResponse }]
        });

        // 保持最近10轮对话
        if (conversationState.conversationHistory.length > 20) {
            conversationState.conversationHistory = conversationState.conversationHistory.slice(-20);
        }

        return openaiResponse;
    } catch (error) {
        console.error('OpenAI API call failed:', error.message);
        return `抱歉，AI 服务暂时无法连线，建议您稍后再试或联系物业服务专线 2656 3800。错误讯息: ${error.message}`;
    }
}

// ===== 测试函数 =====

// 模拟完整的对话处理流程
async function processTestMessage(userId, userInput) {
    console.log(`\n🔄 处理用户消息: "${userInput}"`);
    console.log('=' .repeat(60));

    // 1. 语言检测
    const detectedLanguage = detectLanguage(userInput);
    console.log(`🌍 检测语言: ${detectedLanguage}`);

    // 2. 创建或获取对话状态
    let conversationState = createConversationState(userId);
    conversationState.detectedLanguage = detectedLanguage;

    // 3. 生成多语言确认消息
    const welcomeMessage = getLocalizedMessage('welcome', detectedLanguage);
    const processingMessage = getLocalizedMessage('processing', detectedLanguage);

    console.log(`📱 确认消息: "${processingMessage}"`);

    // 4. 调用LLM获取回复
    console.log(`🤖 调用${LLM_PROVIDER.toUpperCase()}...`);

    try {
        const llmResponse = await getLLMResponse(userInput, NOVOLOG_KNOWLEDGE_BASE, conversationState);
        console.log(`✅ AI回复: "${llmResponse}"`);

        // 5. 检测相关文档
        const relatedDocs = detectRelatedDocuments(llmResponse, NOVOLOG_KNOWLEDGE_BASE);

        if (relatedDocs.length > 0) {
            console.log(`📄 相关文档 (${relatedDocs.length}个):`);
            relatedDocs.forEach((doc, index) => {
                console.log(`   ${index + 1}. ${doc.title}`);
                console.log(`      📎 ${doc.doc_url}`);
                if (doc.reference_no) {
                    console.log(`      🔖 ${doc.reference_no}`);
                }
            });
        } else {
            console.log(`📄 相关文档: 无`);
        }

        return {
            detectedLanguage,
            processingMessage,
            llmResponse,
            relatedDocs
        };

    } catch (error) {
        console.error(`❌ 处理失败: ${error.message}`);
        return null;
    }
}

// 交互式测试模式
async function interactiveTest() {
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log('🎯 对话测试模式');
    console.log('================');
    console.log('输入消息进行测试，输入 "quit" 退出');
    console.log('');

    const askQuestion = () => {
        rl.question('💬 请输入测试消息: ', async (input) => {
            if (input.toLowerCase() === 'quit') {
                console.log('👋 测试结束');
                rl.close();
                return;
            }

            if (input.trim()) {
                await processTestMessage('test-user', input.trim());
            }

            console.log('\n' + '-'.repeat(60));
            askQuestion();
        });
    };

    askQuestion();
}

// 批量测试模式
async function batchTest() {
    console.log('🧪 批量测试模式');
    console.log('================');

    const testCases = [
        { input: '你好，请问升降机什么时候维修？', description: '中文-升降机维修询问' },
        { input: 'Hello, when is the elevator maintenance?', description: '英文-电梯维修询问' },
        { input: 'Hola, ¿cuándo es el mantenimiento del ascensor?', description: '西班牙文-电梯维修询问' },
        { input: '안녕하세요, 엘리베이터 유지보수는 언제인가요?', description: '韩文-电梯维修询问' },
        { input: '台风来了怎么办？', description: '中文-台风防护询问' },
        { input: 'What should I do during a typhoon?', description: '英文-台风防护询问' },
        { input: '窗户试水是什么时候？', description: '中文-水测试询问' },
        { input: '装修噪音太大了', description: '中文-装修投诉' }
    ];

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n📋 测试 ${i + 1}/${testCases.length}: ${testCase.description}`);

        await processTestMessage(`test-user-${i}`, testCase.input);

        // 添加延迟避免API调用过快
        if (i < testCases.length - 1) {
            console.log('\n⏳ 等待2秒...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    console.log('\n🎉 批量测试完成！');
}

// ===== 主程序 =====

async function main() {
    console.log('🎯 WhatsApp Winnie AI 对话测试工具');
    console.log('====================================');
    console.log('');

    // 检查配置
    console.log(`🤖 当前LLM提供商: ${LLM_PROVIDER}`);

    if (LLM_PROVIDER === 'gemini') {
        console.log(`🔑 Gemini API: ${GEMINI_API_KEY ? '已配置' : '未配置'}`);
        if (!GEMINI_API_KEY) {
            console.error('❌ 请设置 GEMINI_API_KEY 环境变量');
            process.exit(1);
        }
    } else if (LLM_PROVIDER === 'openai') {
        console.log(`🔑 OpenAI API: ${OPENAI_API_KEY ? '已配置' : '未配置'}`);
        console.log(`🎯 OpenAI 模型: ${OPENAI_MODEL}`);
        if (!OPENAI_API_KEY) {
            console.error('❌ 请设置 OPENAI_API_KEY 环境变量');
            process.exit(1);
        }
    }

    console.log(`📚 知识库文档: ${Object.keys(NOVOLOG_KNOWLEDGE_BASE).length} 个`);
    console.log('');

    // 获取命令行参数
    const mode = process.argv[2] || 'interactive';

    if (mode === 'batch') {
        await batchTest();
    } else if (mode === 'interactive') {
        await interactiveTest();
    } else {
        console.log('用法:');
        console.log('  node test-conversation.js interactive  # 交互式测试 (默认)');
        console.log('  node test-conversation.js batch        # 批量测试');
    }
}

// 运行主程序
if (require.main === module) {
    main().catch(error => {
        console.error('程序运行错误:', error);
        process.exit(1);
    });
}
