# WhatsApp API 配置
VERIFY_TOKEN=a-very-secret-token-for-my-whatsapp-bot-8642
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
PHONE_NUMBER_ID=your_phone_number_id_here

# 服务器配置
PORT=6767

# LLM 提供商选择: 'gemini' 或 'openai'
LLM_PROVIDER=gemini

# Gemini API 配置 (当 LLM_PROVIDER=gemini 时使用)
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API 配置 (当 LLM_PROVIDER=openai 时使用)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# 注意：
# 1. 将此文件复制为 .env 并填入真实的API密钥
# 2. 确保 .env 文件已添加到 .gitignore 中，避免泄露密钥
# 3. 根据选择的 LLM_PROVIDER，只需要配置对应的API密钥
