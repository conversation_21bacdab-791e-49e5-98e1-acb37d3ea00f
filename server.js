'use strict';

// 加载环境变量
require('dotenv').config();

const express = require('express');
const bodyParser = require('body-parser');
const axios = require('axios');
const OpenAI = require('openai');

const app = express();
app.use(bodyParser.json());

// --- WhatsApp API 配置 ---
const VERIFY_TOKEN = process.env.VERIFY_TOKEN || "a-very-secret-token-for-my-whatsapp-bot-8642";
const WHATSAPP_TOKEN = process.env.WHATSAPP_ACCESS_TOKEN || "";
const PHONE_NUMBER_ID = process.env.PHONE_NUMBER_ID || "648351825038094";
const PORT = process.env.PORT || 6767;

// --- LLM 配置 ---
// LLM 提供商选择: 'gemini' 或 'openai'
const LLM_PROVIDER = process.env.LLM_PROVIDER || 'gemini';

// Gemini API 配置
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'AIzaSyBThYRqpm11nqHG9NSzr1VBON6uclANp-g';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${GEMINI_API_KEY}`;

// OpenAI API 配置
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4o-mini';

// 初始化 OpenAI 客户端
let openai = null;
if (OPENAI_API_KEY) {
    openai = new OpenAI({
        apiKey: OPENAI_API_KEY,
    });
}

// --- 静态知识库 (基于line.js的NOVOLAND知识库) ---
const NOVOLOG_KNOWLEDGE_BASE = {
    "notice_lift_maintenance_NOL-N456-2025.pdf": {
        "metadata": { "file_name": "notice_lift_maintenance_NOL-N456-2025.pdf", "reference_no": "NOL-N456-2025", "date_of_issue": "27/06/2025", "issuing_office": "NOVO LAND Management Services Office" },
        "title": { "zh_hk": "各座升降機例檢暫停服務事宜", "en_us": "Suspension of Lift Services System for Routine Inspection" },
        "target_audience": { "zh_hk": "NOVO LAND 各住戶", "en_us": "All Residents of NOVO LAND" },
        "body": {
            "introduction": { "zh_hk": "現通知各住戶升降機承辦商(通力電梯(香港)有限公司) 將於下列日期及時間進行升降機定期檢驗。", "en_us": "Please be informed that we have arranged contractor (KONE Elevator (HK) Ltd) to provide regular inspection for the lift services system, schedule which is as follows:" },
            "schedule": [
                { "date": "02/07/2025", "day_of_week": { "zh_hk": "星期三", "en_us": "Wednesday" }, "time": "08:30 - 18:30", "affected_locations": ["Arreso Tower 5", "Bergen Tower 1"], "lift_no": "L1 – L3" },
                { "date": "03/07/2025", "day_of_week": { "zh_hk": "星期四", "en_us": "Thursday" }, "time": "08:30 - 18:30", "affected_locations": ["Bergen Tower 2", "Bergen Tower 3"], "lift_no": "L1 – L3" },
                { "date": "04/07/2025", "day_of_week": { "zh_hk": "星期五", "en_us": "Friday" }, "time": "08:30 - 18:30", "affected_locations": ["Charlot Tower 1A & 1B"], "lift_no": "L1 – L6" }
            ],
            "closing_remarks": { "zh_hk": "升降機檢查期間, 升降機將會輪流暫停服務, 如不便之處, 敬請原諒。", "en_us": "During the said period, the lift(s) respectively will be suspended of service alternately. We apologize for any inconvenience caused." }
        },
        "doc_url":"https://i.mij.rip/2025/07/21/5e57701c6881d1b07e7f8ed5e46e91f9.png",
        "contact_info": { "message": { "zh_hk": "敬希垂注！如有任何查詢, 歡迎致電 2656 3800 與服務處職員聯絡。", "en_us": "Should you have any enquiries, please feel free to contact us at 2656 3800." }, "phone": "2656 3800" }
    },
    "notice_typhoon_prevention_NOL-N077-2023.pdf": {
        "metadata": { "file_name": "notice_typhoon_prevention_NOL-N077-2023.pdf", "reference_no": "NOL-N077-2023", "date_of_issue": "15/07/2023", "issuing_office": "NOVO LAND Management Services Office" },
        "title": { "zh_hk": "有關:家居防風措施事宜", "en_us": "Re: Typhoon Prevention of Measures" },
        "target_audience": { "zh_hk": "NOVO LAND 各住戶", "en_us": "All Residents of NOVO LAND" },
        "body": {
            "introduction": { "zh_hk": "根據香港天文台預則, 由於熱帶氣旋將迫近本港, 為保障家居財物及人身安全, 服務處呼籲各業戶於適當時間採取以下的防風措施:", "en_us": "According to the forecast of the Hong Kong Observatory, Tropical Cyclone will be approaching Hong Kong soon. Management Services Office would like to remind our residents of the following preventive measures if deemed necessary:" },
            "preventive_measures": { "zh_hk": ["緊閉及鎖好所有窗戶, 免生意外;", "在玻璃窗貼上膠布, 以防玻璃破裂時而碎片散裂;", "收回晾衣架上的衣物;", "平放或收起一切容易被風吹倒之物件;", "切忌站立於當風位置;", "強風過後, 立即更換破裂之玻璃;"], "en_us": ["Make sure all windows are firmly locked in order to avoid any damage;", "Stretch adhesive tapes over windows panes to prevent flying glass;", "Make sure all clothing are collected inside;", "Put loose objects down or away;", "Do not stay close to those strong wind locations;", "Replace all broken or cracked glass panes as soon as the strong wind is over."] }
        },
        "doc_url":"https://i.mij.rip/2025/07/21/1fc9d9341f202ce0aa0758682e2deaec.png",
        "contact_info": { "message": { "zh_hk": "住戶若對上事宜有任何查詢, 請致電2656 3800與本處職員聯絡。", "en_us": "Should you have any queries, please contact our staff at 2656 3800." }, "phone": "2656 3800" }
    },
    "notice_water_testing_NOL-N053-2023.pdf": {
        "metadata": { "file_name": "notice_water_testing_NOL-N053-2023.pdf", "reference_no": "NOL-N053-2023", "date_of_issue": "04/07/2023", "issuing_office": "NOVO LAND Management Services Office" },
        "title": { "zh_hk": "有關:個別單位室外試水事宜", "en_us": "Re : Outdoor water testing at individual unit" },
        "target_audience": { "zh_hk": "NOVO LAND 各住戶", "en_us": "All Residents of NOVO LAND" },
        "body": {
            "introduction": { "zh_hk": "現 通知各住戶, 部份單位業主已安排承辦商將進行室外(窗戶)試水工程, 有關詳情如下:", "en_us": "Please be informed that individual units have been arranged with contractors to carry out outdoor (windows) water testing. Details are as follows:" },
            "testing_schedule": [
                { "date": "05/07/2023", "day_of_week": { "zh_hk": "星期三", "en_us": "Wednesday" }, "time": "09:00 - 17:00", "affected_units": [{ "tower": "Elverum 1", "floor": "2-5", "unit": "M, N" }, { "tower": "Elverum 2", "floor": "2-23", "unit": "D" }, { "tower": "Bergen 1", "floor": "2-5", "unit": "C, D" }, { "tower": "Bergen 2", "floor": "2-7", "unit": "C, D" }, { "tower": "Bergen 3", "floor": "2-10", "unit": "H, Q, M, R" }] },
                { "date": "07/07/2023", "day_of_week": { "zh_hk": "星期五", "en_us": "Friday" }, "time": "09:00 - 17:00", "affected_units": [{ "tower": "Bergen 1", "floor": "2-21", "unit": "E" }] }
            ],
            "closing_remarks": { "zh_hk": "於室外試水工程進行期間, 請住戶關上有關位置的窗戶, 以免水滴濺入室內。請各住戶收起放置於露台的衣物及物品。", "en_us": "During the course of work, residents are reminded to keep the windows closed to prevent water splash into the indoor area and remove any articles and clothes from the balcony." }
        },
        "doc_url":"https://i.mij.rip/2025/07/21/593de6821f714653b50bd475847eda8f.png",
        "contact_info": { "message": { "zh_hk": "如有任何查詢, 歡迎致電2656 3800 與服務處職員聯絡。", "en_us": "Should you have any enquiries, please contact our management staff at 2656 3800." }, "phone": "2656 3800" }
    },
    "notice_decoration_work_NOL-N034-2023.pdf": {
        "metadata": { "file_name": "notice_decoration_work_NOL-N034-2023.pdf", "reference_no": "NOL-N034-2023", "date_of_issue": "26 June 2023", "posting_date_to": "31/08/2023", "issuing_office": "NOVO LAND Management Services Office" },
        "title": { "zh_hk": "有關:NOVOLAND Elverum 2 單位裝修工程事宜", "en_us": "Re: NOVOLAND Elverum 2 Unit Decoration Work" },
        "target_audience": { "zh_hk": "NOVOLAND Elverum 2 各住戶", "en_us": "All Residents of Elverum 2, NOVOLAND" },
        "body": {
            "introduction": { "zh_hk": "茲 通知各住戶, 下列樓層有單位進行室內裝修工程, 有關詳情如下:", "en_us": "Please be informed that unit decoration works would be carried out at the below floor(s), schedule of which is as follows:" },
            "work_details": { "decoration_unit": { "zh_hk": "裝修單位", "en_us": "Decoration unit", "value_zh": "Elverum 2座 31樓 E室", "value_en": "Flat E, 31/F, Elverum Tower 2" }, "working_period": { "zh_hk": "裝修日期", "en_us": "Working Period", "value_zh": "2023年06月29日至2023年08月31日", "value_en": "From 29 June 2023 to 31 August 2023" }, "working_time": { "zh_hk": "裝修時間", "en_us": "Working Time", "value_zh": "由09:00至18:00", "value_en": "From 09:00 to 18:00" } },
            "closing_remarks": { "zh_hk": "於上述工程期間, 將可能有噪音及/或油漆氣味自裝修單位傳出。敬請見諒! 因保安理由, 亦謹呼籲各單位之住戶將窗戶及大門鎖好, 以免賊人有機可乘。", "en_us": "During the interim, noise may be emitted and / or smell of paint permeated. We apologize for any inconvenience so caused. For security reasons, please be reminded to lock up the windows and main door for the prevention of crime. Thank you for your kind attention." }
        },
        "doc_url":"https://i.mij.rip/2025/07/21/debfba67b07bd1d62d88a4fb22eddbf6.png",
        "contact_info": { "message": { "zh_hk": "敬希垂注！如有任何查詢, 歡迎致電 2656 3800 與服務處職員聯絡。", "en_us": "Should you have any enquiries, please feel free to contact us at 2656 3800." }, "phone": "2656 3800" }
    }
};

// --- 多语言支持配置 ---
const LANGUAGE_MESSAGES = {
    welcome: {
        'zh': '您好！我是 Novoland 的 AI 客服助手 Winnie，已了解社区的所有基本资讯与最新公告。请问有什么可以为您服务？',
        'en': 'Hello! I am Winnie, the AI customer service assistant for Novoland. I have access to all community information and latest announcements. How may I assist you?',
        'es': '¡Hola! Soy Winnie, el asistente de servicio al cliente de IA para Novoland. Tengo acceso a toda la información de la comunidad y los últimos anuncios. ¿Cómo puedo ayudarte?',
        'ko': '안녕하세요! 저는 Novoland의 AI 고객 서비스 어시스턴트 Winnie입니다. 모든 커뮤니티 정보와 최신 공지사항에 접근할 수 있습니다. 어떻게 도와드릴까요?',
        'th': 'สวัสดีค่ะ! ฉันคือ Winnie ผู้ช่วยบริการลูกค้า AI ของ Novoland ฉันมีข้อมูลชุมชนและประกาศล่าสุดทั้งหมด มีอะไรให้ช่วยเหลือคะ?',
        'ja': 'こんにちは！私はNovolandのAIカスタマーサービスアシスタントのWinnieです。コミュニティのすべての情報と最新のお知らせにアクセスできます。何かお手伝いできることはありますか？'
    },
    processing: {
        'zh': 'Winnie 正在为您查询资料，请稍候...',
        'en': 'Winnie is searching for information for you, please wait...',
        'es': 'Winnie está buscando información para ti, por favor espera...',
        'ko': 'Winnie가 정보를 검색하고 있습니다. 잠시만 기다려 주세요...',
        'th': 'Winnie กำลังค้นหาข้อมูลให้คุณ กรุณารอสักครู่...',
        'ja': 'Winnieが情報を検索しています。少々お待ちください...'
    }
};

// --- 用户状态与对话管理 (基于line.js的对话管理系统) ---
const userStatusMap = new Map();
const conversationCache = new Map();
const processedMessages = new Map();
const CONVERSATION_TIMEOUT = 30 * 60 * 1000; // 30分钟过期
const MAX_CONVERSATION_ROUNDS = 30;
const MESSAGE_CACHE_TTL = 30 * 60 * 1000; // 消息去重缓存30分钟

// 语言检测函数
function detectLanguage(text) {
    if (!text || typeof text !== 'string') return 'zh';

    const cleanText = text.toLowerCase().trim();

    // 中文检测（包含中文字符）
    if (/[\u4e00-\u9fff]/.test(text)) {
        return 'zh';
    }

    // 韩语检测
    if (/[\uac00-\ud7af\u1100-\u11ff\u3130-\u318f]/.test(text)) {
        return 'ko';
    }

    // 日语检测
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) {
        return 'ja';
    }

    // 泰语检测
    if (/[\u0e00-\u0e7f]/.test(text)) {
        return 'th';
    }

    // 西班牙语关键词检测
    const spanishKeywords = ['hola', 'gracias', 'por favor', 'ayuda', 'información', 'servicio', 'problema', 'necesito', 'quiero', 'puedo', 'cómo', 'qué', 'dónde', 'cuándo'];
    if (spanishKeywords.some(keyword => cleanText.includes(keyword))) {
        return 'es';
    }

    // 英语关键词检测（默认为英语如果包含常见英语词汇）
    const englishKeywords = ['hello', 'hi', 'help', 'thank', 'please', 'service', 'problem', 'need', 'want', 'can', 'how', 'what', 'where', 'when', 'maintenance', 'lift', 'elevator'];
    if (englishKeywords.some(keyword => cleanText.includes(keyword))) {
        return 'en';
    }

    // 默认返回中文
    return 'zh';
}

// 获取多语言消息
function getLocalizedMessage(messageType, language) {
    const messages = LANGUAGE_MESSAGES[messageType];
    if (!messages) return LANGUAGE_MESSAGES.welcome.zh; // 默认欢迎消息

    return messages[language] || messages.zh || messages.en || Object.values(messages)[0];
}

// 对话状态结构
function createConversationState(userId) {
    return {
        userId: userId,
        rounds: 0,
        startTime: Date.now(),
        lastActivity: Date.now(),
        conversationHistory: [],
        detectedLanguage: 'zh' // 添加语言检测字段
    };
}

// 检查对话是否有效
function isConversationValid(conversationState) {
    const now = Date.now();
    const timeSinceStart = now - conversationState.startTime;
    const timeSinceLastActivity = now - conversationState.lastActivity;

    return conversationState.rounds < MAX_CONVERSATION_ROUNDS &&
        timeSinceStart < CONVERSATION_TIMEOUT &&
        timeSinceLastActivity < CONVERSATION_TIMEOUT;
}

// 清理过期对话和消息缓存
function cleanupExpiredData() {
    const now = Date.now();

    // 清理过期对话
    for (const [userId, state] of conversationCache.entries()) {
        if (!isConversationValid(state)) {
            conversationCache.delete(userId);
            console.log(`--- Cleaned up expired conversation for user: ${userId} ---`);
        }
    }

    // 清理过期消息缓存
    for (const [messageId, timestamp] of processedMessages.entries()) {
        if (now - timestamp > MESSAGE_CACHE_TTL) {
            processedMessages.delete(messageId);
        }
    }
}

// 定期清理任务
setInterval(cleanupExpiredData, 5 * 60 * 1000); // 每5分钟清理一次

// 启动服务器
app.listen(PORT, () => {
    console.log(`WhatsApp Winnie AI客服正在监听端口：${PORT}`);
    console.log(`当前LLM提供商：${LLM_PROVIDER.toUpperCase()}`);

    if (LLM_PROVIDER === 'gemini') {
        console.log(`Gemini API已配置，模型：gemini-2.0-flash-exp`);
    } else if (LLM_PROVIDER === 'openai') {
        console.log(`OpenAI API已配置，模型：${OPENAI_MODEL}`);
    }

    console.log(`知识库已加载 (${Object.keys(NOVOLOG_KNOWLEDGE_BASE).length} 个文档)`);

    if (!VERIFY_TOKEN || !WHATSAPP_TOKEN || !PHONE_NUMBER_ID) {
        console.error("错误：必要的环境变量未设定！请检查 VERIFY_TOKEN, WHATSAPP_TOKEN, PHONE_NUMBER_ID。");
        process.exit(1);
    }

    if (LLM_PROVIDER === 'openai' && !OPENAI_API_KEY) {
        console.error("错误：选择了OpenAI但未设置OPENAI_API_KEY环境变量！");
        process.exit(1);
    }

    if (LLM_PROVIDER === 'gemini' && !GEMINI_API_KEY) {
        console.error("错误：选择了Gemini但未设置GEMINI_API_KEY环境变量！");
        process.exit(1);
    }
});

// Webhook 验证 (GET 请求)
app.get('/webhook', (req, res) => {
    const mode = req.query['hub.mode'];
    const token = req.query['hub.verify_token'];
    const challenge = req.query['hub.challenge'];

    if (mode === 'subscribe' && token === VERIFY_TOKEN) {
        console.log('Webhook 验证成功！');
        res.status(200).send(challenge);
    } else {
        console.error('验证失败：Verify Token 不匹配。');
        res.sendStatus(403);
    }
});

// 接收 WhatsApp 消息通知 (POST 请求) - 双阶段回复机制
app.post('/webhook', async (req, res) => {
    try {
        const body = req.body;
        console.log('--- [1] WhatsApp Webhook Request Received ---');

        // 随机清理过期数据
        if (Math.random() < 0.1) {
            cleanupExpiredData();
        }

        // 检查是否为 WhatsApp 的消息
        if (body.object === 'whatsapp_business_account' &&
            body.entry &&
            body.entry[0].changes &&
            body.entry[0].changes[0].value.messages) {

            const message = body.entry[0].changes[0].value.messages[0];
            const messageId = message.id;
            const from = message.from;
            const msgBody = message.text ? message.text.body : "";

            console.log(`--- [2] Message from ${from} (ID: ${messageId}): ${msgBody} ---`);

            // 防重复处理
            if (processedMessages.has(messageId)) {
                console.log(`--- Message ${messageId} already processed, skipping ---`);
                res.sendStatus(200);
                return;
            }

            // 标记消息为已处理
            processedMessages.set(messageId, Date.now());

            // 只处理文本消息
            if (!message.text || !msgBody.trim()) {
                console.log('--- Skipping non-text or empty message ---');
                res.sendStatus(200);
                return;
            }

            // 异步处理双阶段回复
            setImmediate(() => {
                processWhatsAppMessage(from, msgBody, messageId)
                    .catch(error => {
                        console.error('--- [ERROR] Processing WhatsApp message:', error);
                    });
            });

            res.sendStatus(200);
        } else {
            res.sendStatus(404);
        }
    } catch (error) {
        console.error('--- [ERROR] Webhook processing error:', error);
        res.sendStatus(500);
    }
});

/**
 * 双阶段WhatsApp消息处理 (基于line.js的处理逻辑)
 */
async function processWhatsAppMessage(from, userInput, messageId) {
    console.log(`--- [3] Starting dual-stage processing for user ${from} ---`);

    try {
        // 多轮对话状态管理
        let conversationState = conversationCache.get(from);
        let isNewConversation = false;

        if (!conversationState || !isConversationValid(conversationState)) {
            conversationState = createConversationState(from);
            conversationCache.set(from, conversationState);
            isNewConversation = true;
            console.log(`--- [4] Started new conversation for user: ${from} ---`);
        } else {
            conversationState.rounds++;
            conversationState.lastActivity = Date.now();
            console.log(`--- [4] Continuing conversation for user: ${from}, round: ${conversationState.rounds} ---`);
        }

        // 检测用户语言
        const detectedLanguage = detectLanguage(userInput);
        conversationState.detectedLanguage = detectedLanguage;
        console.log(`--- [4.1] Detected language: ${detectedLanguage} ---`);

        // 【第一阶段】立即发送确认回复 (基于line.js的回复逻辑)
        let quickReplyMessage;
        if (isNewConversation && !userStatusMap.has(from)) {
            // 全新用户 - 使用检测到的语言发送欢迎消息
            quickReplyMessage = getLocalizedMessage('welcome', detectedLanguage);
            userStatusMap.set(from, true);
        } else {
            // 续对话用户 - 使用检测到的语言发送处理消息
            quickReplyMessage = getLocalizedMessage('processing', detectedLanguage);
        }

        // 立即发送确认消息
        await sendWhatsAppMessage(from, quickReplyMessage);
        console.log(`--- [5] Sent immediate reply: ${quickReplyMessage} ---`);

        // 【第二阶段】异步LLM AI查询和回复
        console.log(`--- [6] Starting ${LLM_PROVIDER.toUpperCase()} AI query ---`);

        // 获取知识库 (每次调用都获取最新知识库)
        const knowledgeBase = await getKnowledgeBase();
        console.log(`--- [6.1] Knowledge base loaded: ${Object.keys(knowledgeBase).length} documents ---`);

        // 调用 LLM AI (支持Gemini和OpenAI)
        const llmResponse = await getLLMResponse(userInput, knowledgeBase, conversationState);
        console.log(`--- [7] Got ${LLM_PROVIDER.toUpperCase()} response: ${llmResponse.substring(0, 200)}... ---`);

        let finalMessage;
        try {
            // 解析LLM返回的JSON格式回复（主要针对Gemini）
            const parsedResponse = JSON.parse(llmResponse);
            if (Array.isArray(parsedResponse) && parsedResponse.length > 0 && parsedResponse[0].text) {
                finalMessage = parsedResponse[0].text;
            } else {
                finalMessage = llmResponse;
            }
        } catch (e) {
            console.log(`--- ${LLM_PROVIDER.toUpperCase()} response is not JSON, using as plain text ---`);
            finalMessage = llmResponse;
        }

        // 发送AI生成的最终回复
        await sendWhatsAppMessage(from, finalMessage);
        console.log(`--- [8] Sent AI response to user ${from} ---`);

        // 【第三阶段】检测并发送相关文档图片
        try {
            await detectAndSendRelatedDocuments(from, finalMessage, knowledgeBase);
        } catch (docError) {
            console.error('--- [ERROR] Failed to send related documents:', docError.message);
            // 文档发送失败不影响主要功能，只记录错误
        }

    } catch (error) {
        console.error('--- [ERROR] Error in processWhatsAppMessage:', error);

        // 发送错误提示
        const errorMessage = `抱歉，AI服务暂时无法连线，建议您稍后再试或联系物业服务专线 2656 3800。错误: ${error.message}`;
        await sendWhatsAppMessage(from, errorMessage)
            .catch(sendError => console.error('--- [ERROR] Failed to send error message:', sendError));
    }
}

/**
 * 获取静态知识库 (基于line.js的知识库加载)
 */
async function getKnowledgeBase() {
    console.log("--- Loading knowledge base from static variable ---");
    return NOVOLOG_KNOWLEDGE_BASE;
}

/**
 * 检测回复内容并发送相关文档图片
 */
async function detectAndSendRelatedDocuments(to, responseText, knowledgeBase) {
    if (!responseText || !knowledgeBase) return;

    console.log('--- [DOC] Checking for related documents ---');

    const lowerResponseText = responseText.toLowerCase();
    const sentDocuments = new Set(); // 防止重复发送同一文档

    // 检查每个知识库文档
    for (const [docKey, docData] of Object.entries(knowledgeBase)) {
        if (!docData.doc_url) continue;

        let shouldSendDoc = false;

        // 检查标题关键词
        if (docData.title) {
            const titleKeywords = [
                ...(docData.title.zh_hk ? docData.title.zh_hk.split(/[，。！？\s]+/) : []),
                ...(docData.title.en_us ? docData.title.en_us.split(/[,.\s]+/) : [])
            ].filter(keyword => keyword.length > 2);

            if (titleKeywords.some(keyword => lowerResponseText.includes(keyword.toLowerCase()))) {
                shouldSendDoc = true;
            }
        }

        // 检查特定关键词
        const documentKeywords = {
            'notice_lift_maintenance': ['升降機', '电梯', 'lift', 'elevator', '维修', 'maintenance', '检验', 'inspection'],
            'notice_typhoon_prevention': ['台风', '防风', 'typhoon', '风暴', 'storm', '防护', 'prevention'],
            'notice_water_testing': ['试水', '水测试', 'water testing', '窗户', 'window', '测试', 'testing'],
            'notice_decoration_work': ['装修', '装潢', 'decoration', 'renovation', '工程', 'work', '噪音', 'noise']
        };

        // 根据文档类型检查关键词
        for (const [docType, keywords] of Object.entries(documentKeywords)) {
            if (docKey.includes(docType.split('_')[1])) { // 匹配文档类型
                if (keywords.some(keyword => lowerResponseText.includes(keyword.toLowerCase()))) {
                    shouldSendDoc = true;
                    break;
                }
            }
        }

        // 检查参考编号
        if (docData.metadata && docData.metadata.reference_no) {
            if (lowerResponseText.includes(docData.metadata.reference_no.toLowerCase())) {
                shouldSendDoc = true;
            }
        }

        // 发送文档图片
        if (shouldSendDoc && !sentDocuments.has(docData.doc_url)) {
            try {
                console.log(`--- [DOC] Sending document image: ${docKey} ---`);

                // 构建图片说明
                let caption = '';
                if (docData.title && docData.title.zh_hk) {
                    caption = `📄 ${docData.title.zh_hk}`;
                    if (docData.metadata && docData.metadata.reference_no) {
                        caption += ` (${docData.metadata.reference_no})`;
                    }
                }

                await sendWhatsAppImage(to, docData.doc_url, caption);
                sentDocuments.add(docData.doc_url);

                // 添加小延迟避免消息发送过快
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                console.error(`--- [ERROR] Failed to send document image for ${docKey}:`, error.message);
            }
        }
    }

    if (sentDocuments.size > 0) {
        console.log(`--- [DOC] Sent ${sentDocuments.size} related document(s) ---`);
    }
}

/**
 * 通用LLM调用函数 - 支持Gemini和OpenAI
 */
async function getLLMResponse(userInput, knowledgeBase, conversationState) {
    if (LLM_PROVIDER === 'openai') {
        return await getOpenAIResponse(userInput, knowledgeBase, conversationState);
    } else {
        return await getGeminiResponse(userInput, knowledgeBase, conversationState);
    }
}

/**
 * Gemini AI 调用 (基于line.js的Gemini集成，适配WhatsApp格式)
 */
async function getGeminiResponse(userInput, knowledgeBase, conversationState) {
    // 每次调用都包含知识库的系统提示
    const knowledgeBaseString = JSON.stringify(knowledgeBase, null, 2);
    const systemPrompt = `
#1. 总体目标 (Overall Objective)
你将扮演「Winnie」，一个专为「Novoland」高档住宅小区设计的 AI 客服助手。你的核心目标是为住户与来宾提供即时、准确且友善的资讯与指引，并根据对话类型遵循严格的资讯核实流程。

#2. 角色设定：Winnie (Persona)
- **身份**：Novoland 住宅小区的 AI 客服总管。
- **语气与风格**：温暖专业、耐心有礼、用词清晰。
- **沟通惯例**：
    - **称谓**：必须使用「先生/女士/住户/来宾」等称谓。
    - **问候**：对话开始时，必须根据时间使用「您好」、「早安」、「午安」或「晚安」。
    - **结语**：在对话自然结束或完成一项任务后，必须主动询问「请问还有其他可以协助您的地方吗？」。
    - **语言**： 你必须侦测用户所使用的语言（例如繁体中文、英文、西班牙文、韩文、泰文、日文等），并总是使用「相同的语言」进行回复。你的主要服务语言为繁体中文，但要以用户的语言为优先。系统已检测到用户语言为：${conversationState.detectedLanguage}，请使用此语言回复。

#3. 核心知识库 (Knowledge Base)
你已预载 Novoland 的公开资讯，包括最新的社区公告。请根据此资讯回答相关问题：
${knowledgeBaseString}

#4. 【关键任务】分层回答与资讯核实流程
你必须根据用户问题的类型，执行对应的对话流程。

**层级一：基础问题 (无需核实资讯)**
- **触发条件**：用户询问公共设施开放时间、地点、使用规则、缴费途径等一般性问题。
- **执行动作**：直接从知识库中提供准确答案。
- **范例**：「请问健身房几点开？」-> 你应直接回答：「您好！健身房的开放时间为...」

**层级二：个性化服务 (一次性获取资讯)**
- **触发条件**：用户提出维修申请、预约特定服务、或需要物业跟进的非紧急请求（如「厨房漏水」、「预约会所烧烤场」）。
- **执行流程**：
    1.  **一次性询问**：识别出用户意图后，将需要的多个资讯（如地址、姓名）合并在一个问题中礼貌地提出。
        - **话术范例**：「好的，为您处理维修事宜。为加快流程，请问您需要服务的**座数与楼层**，以及**如何称呼申请人**呢？请您一次过告诉我，谢谢！」
    2.  **等待并处理回覆**：等待用户提供所有资讯。用户可能会说「8座21楼，姓李」。
    3.  **确认并总结**：收到资讯后，解析并总结用户的请求。
        - **话术范例**：「好的，已为居住在8座21楼的李先生/女士登记厨房漏水维修服务。」
    4.  **(可选) 询问电话**：在总结后，你可以「建议」用户留下电话以加快处理。话术：「工程部门预计将在2小时内处理。如果需要他们直接致电给您，可以留下您的联络电话，这个号码仅会用于本次维修联络，通话结束后系统不会保留。」

**层级三：投诉或紧急事件 (优先转介)**
- **触发条件**：用户表达强烈不满、使用投诉字眼、或报告紧急情况（如被困电梯、火警警报）。
- **执行流程**：
    1.  **安抚情绪并快速定位**：立即回应「我明白您的情况，请告诉我您目前在哪一座大楼？」
    2.  **立即提供专线**：一旦获取到基本资讯，立即提供专线电话，不要试图自行解决。话术：「感谢您提供资讯。为确保您的问题能得到最快处理，建议您立即致电我们的24小时紧急服务专线 2656 3800，专人将为您提供即时协助。」

#5. 隐私与合规性
- **绝对禁止**：主动或被动询问用户的完整房号（如 8座21楼A室）。地址资讯只收集到「座」和「楼层」。
- **电话隐私声明**：当你「建议」用户提供电话时，必须附上隐私声明：「您的个人资料仅用于本次服务，通话结束后不会保留，不会用于其他用途。」
- **姓名非强制**：如果用户不愿提供姓名，礼貌回应「好的，我们将以匿名方式为您登记。」并继续流程。

#6. 【重要】输出格式 (Output Format)
- **WhatsApp适配**：你的回复应该是简洁的纯文本格式，适合WhatsApp消息显示。不要使用JSON格式或特殊标记。
- **回复结构**：直接提供友善、专业的文本回复，包含必要的换行来提高可读性。
- **范例回复**：「午安！我是Winnie，Novoland的AI客服助手。请问有什么可以为您服务的吗？」
`;

    let contents = [];

    // 每次都添加系统提示（包含最新知识库）
    contents.push({
        role: "user",
        parts: [{ text: systemPrompt }]
    });

    contents.push({
        role: "model",
        parts: [{ text: "您好！我是 Novoland 的 AI 客服助手 Winnie，已了解社区的所有基本资讯与最新公告。请问有什么可以为您服务？" }]
    });

    // 添加历史对话记录（如果存在）
    if (conversationState.conversationHistory && conversationState.conversationHistory.length > 2) {
        // 跳过前两条（系统提示和初始回复），只保留用户对话历史
        const historyWithoutSystem = conversationState.conversationHistory.slice(2);
        contents.push(...historyWithoutSystem);
    }

    contents.push({
        role: "user",
        parts: [{ text: userInput }]
    });

    try {
        const response = await axios({
            method: 'POST',
            url: GEMINI_API_URL,
            headers: { 'Content-Type': 'application/json' },
            data: {
                "contents": contents,
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            },
            timeout: 30000 // 30秒超时
        });

        const responseData = response.data;

        // 确保 candidates 数组存在且有内容
        if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error('Gemini API returned no candidates in response.');
        }

        const geminiResponse = responseData.candidates[0].content.parts[0].text;

        contents.push({
            role: "model",
            parts: [{ text: geminiResponse }]
        });

        // 保持最近10轮对话（20条消息）
        if (contents.length > 20) {
            contents = contents.slice(-20);
        }
        conversationState.conversationHistory = contents;

        return geminiResponse;
    } catch (error) {
        console.error('--- [ERROR] Gemini API call failed:', error.message);
        return `抱歉，AI 服务暂时无法连线，建议您稍后再试或联系物业服务专线 2656 3800。错误讯息: ${error.message}`;
    }
}

/**
 * OpenAI API 调用
 */
async function getOpenAIResponse(userInput, knowledgeBase, conversationState) {
    if (!openai) {
        throw new Error('OpenAI client not initialized. Please check OPENAI_API_KEY.');
    }

    // 构建知识库系统提示
    const knowledgeBaseString = JSON.stringify(knowledgeBase, null, 2);
    const systemPrompt = `
#1. 总体目标 (Overall Objective)
你将扮演「Winnie」，一个专为「Novoland」高档住宅小区设计的 AI 客服助手。你的核心目标是为住户与来宾提供即时、准确且友善的资讯与指引，并根据对话类型遵循严格的资讯核实流程。

#2. 角色设定：Winnie (Persona)
- **身份**：Novoland 住宅小区的 AI 客服总管。
- **语气与风格**：温暖专业、耐心有礼、用词清晰。
- **沟通惯例**：
    - **称谓**：必须使用「先生/女士/住户/来宾」等称谓。
    - **问候**：对话开始时，必须根据时间使用「您好」、「早安」、「午安」或「晚安」。
    - **结语**：在对话自然结束或完成一项任务后，必须主动询问「请问还有其他可以协助您的地方吗？」。
    - **语言**： 你必须侦测用户所使用的语言（例如繁体中文、英文、西班牙文、韩文、泰文、日文等），并总是使用「相同的语言」进行回复。你的主要服务语言为繁体中文，但要以用户的语言为优先。系统已检测到用户语言为：${conversationState.detectedLanguage}，请使用此语言回复。

#3. 核心知识库 (Knowledge Base)
你已预载 Novoland 的公开资讯，包括最新的社区公告。请根据此资讯回答相关问题：
${knowledgeBaseString}

#4. 【关键任务】分层回答与资讯核实流程
你必须根据用户问题的类型，执行对应的对话流程。

**层级一：基础问题 (无需核实资讯)**
- **触发条件**：用户询问公共设施开放时间、地点、使用规则、缴费途径等一般性问题。
- **执行动作**：直接从知识库中提供准确答案。
- **范例**：「请问健身房几点开？」-> 你应直接回答：「您好！健身房的开放时间为...」

**层级二：个性化服务 (一次性获取资讯)**
- **触发条件**：用户提出维修申请、预约特定服务、或需要物业跟进的非紧急请求（如「厨房漏水」、「预约会所烧烤场」）。
- **执行流程**：
    1.  **一次性询问**：识别出用户意图后，将需要的多个资讯（如地址、姓名）合并在一个问题中礼貌地提出。
        - **话术范例**：「好的，为您处理维修事宜。为加快流程，请问您需要服务的**座数与楼层**，以及**如何称呼申请人**呢？请您一次过告诉我，谢谢！」
    2.  **等待并处理回覆**：等待用户提供所有资讯。用户可能会说「8座21楼，姓李」。
    3.  **确认并总结**：收到资讯后，解析并总结用户的请求。
        - **话术范例**：「好的，已为居住在8座21楼的李先生/女士登记厨房漏水维修服务。」
    4.  **(可选) 询问电话**：在总结后，你可以「建议」用户留下电话以加快处理。话术：「工程部门预计将在2小时内处理。如果需要他们直接致电给您，可以留下您的联络电话，这个号码仅会用于本次维修联络，通话结束后系统不会保留。」

**层级三：投诉或紧急事件 (优先转介)**
- **触发条件**：用户表达强烈不满、使用投诉字眼、或报告紧急情况（如被困电梯、火警警报）。
- **执行流程**：
    1.  **安抚情绪并快速定位**：立即回应「我明白您的情况，请告诉我您目前在哪一座大楼？」
    2.  **立即提供专线**：一旦获取到基本资讯，立即提供专线电话，不要试图自行解决。话术：「感谢您提供资讯。为确保您的问题能得到最快处理，建议您立即致电我们的24小时紧急服务专线 2656 3800，专人将为您提供即时协助。」

#5. 隐私与合规性
- **绝对禁止**：主动或被动询问用户的完整房号（如 8座21楼A室）。地址资讯只收集到「座」和「楼层」。
- **电话隐私声明**：当你「建议」用户提供电话时，必须附上隐私声明：「您的个人资料仅用于本次服务，通话结束后不会保留，不会用于其他用途。」
- **姓名非强制**：如果用户不愿提供姓名，礼貌回应「好的，我们将以匿名方式为您登记。」并继续流程。

#6. 【重要】输出格式 (Output Format)
- **WhatsApp适配**：你的回复应该是简洁的纯文本格式，适合WhatsApp消息显示。不要使用JSON格式或特殊标记。
- **回复结构**：直接提供友善、专业的文本回复，包含必要的换行来提高可读性。
- **范例回复**：「午安！我是Winnie，Novoland的AI客服助手。请问有什么可以为您服务的吗？」
`;

    try {
        // 构建消息历史
        let messages = [
            {
                role: "system",
                content: systemPrompt
            }
        ];

        // 添加对话历史（如果存在）
        if (conversationState.conversationHistory && conversationState.conversationHistory.length > 0) {
            // 转换Gemini格式的历史记录为OpenAI格式
            for (const item of conversationState.conversationHistory) {
                if (item.role === "user") {
                    messages.push({
                        role: "user",
                        content: item.parts[0].text
                    });
                } else if (item.role === "model") {
                    messages.push({
                        role: "assistant",
                        content: item.parts[0].text
                    });
                }
            }
        }

        // 添加当前用户输入
        messages.push({
            role: "user",
            content: userInput
        });

        const completion = await openai.chat.completions.create({
            model: OPENAI_MODEL,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1024,
        });

        const openaiResponse = completion.choices[0].message.content;

        // 更新对话历史（转换为Gemini格式以保持兼容性）
        if (!conversationState.conversationHistory) {
            conversationState.conversationHistory = [];
        }

        conversationState.conversationHistory.push({
            role: "user",
            parts: [{ text: userInput }]
        });

        conversationState.conversationHistory.push({
            role: "model",
            parts: [{ text: openaiResponse }]
        });

        // 保持最近10轮对话（20条消息）
        if (conversationState.conversationHistory.length > 20) {
            conversationState.conversationHistory = conversationState.conversationHistory.slice(-20);
        }

        return openaiResponse;
    } catch (error) {
        console.error('--- [ERROR] OpenAI API call failed:', error.message);
        return `抱歉，AI 服务暂时无法连线，建议您稍后再试或联系物业服务专线 2656 3800。错误讯息: ${error.message}`;
    }
}

/**
 * 发送 WhatsApp 文本消息
 */
async function sendWhatsAppMessage(to, text) {
    if (!text || !text.trim()) {
        console.warn('--- Attempted to send empty message, skipping ---');
        return;
    }

    console.log(`--- Sending WhatsApp message to ${to}: ${text.substring(0, 100)}... ---`);

    const API_VERSION = 'v20.0';
    const url = `https://graph.facebook.com/${API_VERSION}/${PHONE_NUMBER_ID}/messages`;

    try {
        const response = await axios({
            method: 'post',
            url: url,
            headers: {
                'Authorization': `Bearer ${WHATSAPP_TOKEN}`,
                'Content-Type': 'application/json'
            },
            data: {
                messaging_product: 'whatsapp',
                to: to,
                type: 'text',
                text: {
                    body: text.trim()
                }
            },
            timeout: 10000 // 10秒超时
        });

        console.log("--- WhatsApp message sent successfully! ---", response.data?.messages?.[0]?.id || '');
    } catch (error) {
        if (error.response) {
            console.error("--- WhatsApp message send failed:", {
                status: error.response.status,
                data: error.response.data
            });
        } else {
            console.error("--- WhatsApp message network error:", error.message);
        }
        throw error;
    }
}

/**
 * 发送 WhatsApp 图片消息
 */
async function sendWhatsAppImage(to, imageUrl, caption = '') {
    if (!imageUrl || !imageUrl.trim()) {
        console.warn('--- Attempted to send empty image URL, skipping ---');
        return;
    }

    console.log(`--- Sending WhatsApp image to ${to}: ${imageUrl} ---`);

    const API_VERSION = 'v20.0';
    const url = `https://graph.facebook.com/${API_VERSION}/${PHONE_NUMBER_ID}/messages`;

    try {
        const messageData = {
            messaging_product: 'whatsapp',
            to: to,
            type: 'image',
            image: {
                link: imageUrl.trim()
            }
        };

        // 如果有说明文字，添加到图片消息中
        if (caption && caption.trim()) {
            messageData.image.caption = caption.trim();
        }

        const response = await axios({
            method: 'post',
            url: url,
            headers: {
                'Authorization': `Bearer ${WHATSAPP_TOKEN}`,
                'Content-Type': 'application/json'
            },
            data: messageData,
            timeout: 10000 // 10秒超时
        });

        console.log("--- WhatsApp image sent successfully! ---", response.data?.messages?.[0]?.id || '');
    } catch (error) {
        if (error.response) {
            console.error("--- WhatsApp image send failed:", {
                status: error.response.status,
                data: error.response.data
            });
        } else {
            console.error("--- WhatsApp image network error:", error.message);
        }
        throw error;
    }
}

// 健康检查端点
app.get('/', (req, res) => {
    res.json({
        status: 'ok',
        service: 'WhatsApp Winnie AI客服服务',
        timestamp: new Date().toISOString(),
        activeConversations: conversationCache.size,
        processedMessages: processedMessages.size,
        knowledgeBase: `${Object.keys(NOVOLOG_KNOWLEDGE_BASE).length} 个文档`,
        version: '1.0.0'
    });
});

// 系统状态端点
app.get('/status', (req, res) => {
    res.json({
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        activeConversations: conversationCache.size,
        processedMessages: processedMessages.size,
        llmProvider: LLM_PROVIDER,
        llmConfig: {
            geminiAPI: GEMINI_API_KEY ? 'configured' : 'not configured',
            openaiAPI: OPENAI_API_KEY ? 'configured' : 'not configured',
            currentModel: LLM_PROVIDER === 'openai' ? OPENAI_MODEL : 'gemini-2.0-flash-exp'
        },
        whatsappAPI: WHATSAPP_TOKEN ? 'configured' : 'not configured',
        knowledgeBase: {
            documentCount: Object.keys(NOVOLOG_KNOWLEDGE_BASE).length,
            loaded: true
        },
        lastCleanup: new Date().toISOString()
    });
});

// 清理缓存端点 (管理用)
app.post('/admin/clear-cache', (req, res) => {
    const beforeConversations = conversationCache.size;
    const beforeMessages = processedMessages.size;

    conversationCache.clear();
    processedMessages.clear();

    console.log(`--- Admin cache cleanup: ${beforeConversations} conversations, ${beforeMessages} messages cleared ---`);
    res.json({
        message: 'All caches cleared successfully',
        conversationsCleared: beforeConversations,
        messagesCleared: beforeMessages,
        timestamp: new Date().toISOString()
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('--- [ERROR] Server error:', error);
    res.status(500).json({
        error: '服务器内部错误',
        timestamp: new Date().toISOString()
    });
});

// 优雅关闭处理
process.on('SIGTERM', () => {
    console.log('--- Received SIGTERM, shutting down gracefully ---');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('--- Received SIGINT, shutting down gracefully ---');
    process.exit(0);
});

console.log('--- WhatsApp Winnie AI服务已就绪 ---');

module.exports = app;