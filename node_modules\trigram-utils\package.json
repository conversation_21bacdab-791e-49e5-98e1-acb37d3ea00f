{"name": "trigram-utils", "version": "2.0.1", "description": "A few language trigram utilities", "license": "MIT", "keywords": ["trigram", "n-gram", "language", "utilities"], "repository": "wooorm/trigram-utils", "bugs": "https://github.com/wooorm/trigram-utils/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "dependencies": {"collapse-white-space": "^2.0.0", "n-gram": "^2.0.0"}, "devDependencies": {"@types/tape": "^4.0.0", "c8": "^7.0.0", "prettier": "^2.0.0", "remark-cli": "^10.0.0", "remark-preset-wooorm": "^9.0.0", "rimraf": "^3.0.0", "tape": "^5.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.46.0"}, "scripts": {"prepublishOnly": "npm run build && npm run format", "build": "rimraf \"*.d.ts\" && tsc && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --branches 100 --functions 100 --lines 100 --statements 100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}